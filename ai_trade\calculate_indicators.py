from binance_api import (
    get_kline_1m, get_kline_5m, get_kline_15m,
    get_kline_1h, get_kline_4h, get_kline_1d
)
import talib
import numpy as np
from pprint import pprint
from langchain_core.tools import tool
from typing import Dict, List, Optional, Tuple

@tool
def get_kline_data(timeframe: str, symbol: Optional[str] = None) -> Tuple[np.ndarray, np.ndarray, np.ndarray, int]:
    """獲取 K 線資訊並返回價格數據

    Args:
        timeframe: 時間框架 ('1分鐘', '5分鐘', '15分鐘', '1小時', '4小時', '1天')
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含收盤價、最高價、最低價和數據數量的元組
    """
    count = 0
    if timeframe == '1分鐘':
        kline_info = get_kline_1m(symbol)
        count = 30
    elif timeframe == '5分鐘':
        kline_info = get_kline_5m(symbol)
        count = 120
    elif timeframe == '15分鐘':
        kline_info = get_kline_15m(symbol)
        count = 120
    elif timeframe == '1小時':
        kline_info = get_kline_1h(symbol)
        count = 50
    elif timeframe == '4小時':
        kline_info = get_kline_4h(symbol)
        count = 20
    elif timeframe == '1天':
        kline_info = get_kline_1d(symbol)
        count = 7
    else:
        raise ValueError(f"不支持的時間框架: {timeframe}")

    closing_prices = np.array([float(kline[4]) for kline in kline_info])
    high_prices = np.array([float(kline[2]) for kline in kline_info])
    low_prices = np.array([float(kline[3]) for kline in kline_info])
    return closing_prices, high_prices, low_prices, count

@tool
def calculate_moving_average(closing_prices: List[float], period: int = 14) -> np.ndarray:
    """計算簡單移動平均線 (SMA)

    Args:
        closing_prices: 收盤價格列表
        period: 計算週期，默認為 14

    Returns:
        移動平均線數組
    """
    return talib.SMA(np.array(closing_prices), timeperiod=period)

@tool
def calculate_atr(high_prices: List[float], low_prices: List[float], closing_prices: List[float], period: int = 14) -> np.ndarray:
    """計算真實波幅 (ATR)

    Args:
        high_prices: 最高價格列表
        low_prices: 最低價格列表
        closing_prices: 收盤價格列表
        period: 計算週期，默認為 14

    Returns:
        ATR 數組
    """
    return talib.ATR(np.array(high_prices), np.array(low_prices), np.array(closing_prices), timeperiod=period)

@tool
def calculate_rsi(closing_prices: List[float], period: int) -> np.ndarray:
    """計算相對強弱指數 (RSI)

    Args:
        closing_prices: 收盤價格列表
        period: 計算週期

    Returns:
        RSI 數組
    """
    return talib.RSI(np.array(closing_prices), timeperiod=period)

@tool
def calculate_ema(closing_prices: List[float], period: int = 14) -> np.ndarray:
    """計算指數移動平均線 (EMA)

    Args:
        closing_prices: 收盤價格列表
        period: 計算週期，默認為 14

    Returns:
        EMA 數組
    """
    return talib.EMA(np.array(closing_prices), timeperiod=period)

@tool
def calculate_macd(closing_prices: List[float]) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
    """計算 MACD

    Args:
        closing_prices: 收盤價格列表

    Returns:
        包含 MACD、信號線和直方圖的元組
    """
    return talib.MACD(np.array(closing_prices), fastperiod=12, slowperiod=26, signalperiod=9)

@tool
def calculate_kdj(high_prices: List[float], low_prices: List[float], closing_prices: List[float]) -> Tuple[np.ndarray, np.ndarray]:
    """計算 KDJ

    Args:
        high_prices: 最高價格列表
        low_prices: 最低價格列表
        closing_prices: 收盤價格列表

    Returns:
        包含 K 值和 D 值的元組
    """
    return talib.STOCHF(np.array(high_prices), np.array(low_prices), np.array(closing_prices), fastk_period=14, fastd_period=3, fastd_matype=0)

@tool
def get_kline_and_calculate_indicators(timeframe: str, symbol: Optional[str] = None) -> Dict:
    """獲取 K 線資訊並計算技術指標

    Args:
        timeframe: 時間框架 ('1分鐘', '5分鐘', '15分鐘', '1小時', '4小時', '1天')
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含所有技術指標的字典
    """
    closing_prices, high_prices, low_prices, count = get_kline_data(timeframe, symbol)

    # 計算各種技術指標
    atr = calculate_atr(high_prices.tolist(), low_prices.tolist(), closing_prices.tolist())
    ma_7 = calculate_moving_average(closing_prices.tolist(), 7)
    ma_14 = calculate_moving_average(closing_prices.tolist(), 14)
    rsi_6 = calculate_rsi(closing_prices.tolist(), 6)
    rsi_12 = calculate_rsi(closing_prices.tolist(), 12)
    rsi_24 = calculate_rsi(closing_prices.tolist(), 24)
    ema = calculate_ema(closing_prices.tolist())
    macd, macd_signal, macd_hist = calculate_macd(closing_prices.tolist())
    slowk, slowd = calculate_kdj(high_prices.tolist(), low_prices.tolist(), closing_prices.tolist())

    return {
        "atr": atr[-count:],
        "moving_average_7": ma_7[-count:],
        "moving_average_14": ma_14[-count:],
        "rsi_6": rsi_6[-count:],
        "rsi_12": rsi_12[-count:],
        "rsi_24": rsi_24[-count:],
        "ema": ema[-count:],
        "macd": macd[-count:],
        "macd_signal": macd_signal[-count:],
        "macd_hist": macd_hist[-count:],
        "kdj_k": slowk[-count:],
        "kdj_d": slowd[-count:],
    }

# 為了向後兼容，提供一個包裝類
class TechnicalIndicators:
    """向後兼容的 TechnicalIndicators 類，內部使用 LangChain 工具"""

    def __init__(self, symbol: str, time_frame: str):
        self.symbol = symbol
        self.timeframe = time_frame
        self.count = self._get_count_for_timeframe(time_frame)

    def _get_count_for_timeframe(self, timeframe: str) -> int:
        """根據時間框架獲取數據數量"""
        count_map = {
            '1分鐘': 30,
            '5分鐘': 120,
            '15分鐘': 120,
            '1小時': 50,
            '4小時': 20,
            '1天': 7
        }
        return count_map.get(timeframe, 30)

    def get_kline_data(self):
        """獲取 K 線資訊"""
        closing_prices, high_prices, low_prices, _ = get_kline_data(self.timeframe, self.symbol)
        return closing_prices, high_prices, low_prices

    def calculate_moving_average_14(self, closing_prices, period=14):
        """計算簡單移動平均線 (SMA)"""
        return calculate_moving_average(closing_prices.tolist(), period)

    def calculate_moving_average_7(self, closing_prices, period=7):
        """計算簡單移動平均線 (SMA)"""
        return calculate_moving_average(closing_prices.tolist(), period)

    def calculate_atr(self, high_prices, low_prices, closing_prices, period=14):
        """計算真實波幅 (ATR)"""
        return calculate_atr(high_prices.tolist(), low_prices.tolist(), closing_prices.tolist(), period)

    def calculate_rsi(self, closing_prices, period):
        """計算相對強弱指數 (RSI)"""
        return calculate_rsi(closing_prices.tolist(), period)

    def calculate_ema(self, closing_prices, period=14):
        """計算指數移動平均線 (EMA)"""
        return calculate_ema(closing_prices.tolist(), period)

    def calculate_macd(self, closing_prices):
        """計算 MACD"""
        return calculate_macd(closing_prices.tolist())

    def calculate_kdj(self, high_prices, low_prices, closing_prices):
        """計算 KDJ"""
        return calculate_kdj(high_prices.tolist(), low_prices.tolist(), closing_prices.tolist())

    def get_kline_and_calculate_indicators(self):
        """獲取 K 線資訊並計算技術指標"""
        return get_kline_and_calculate_indicators(self.timeframe, self.symbol)

if __name__ == "__main__":
    # 測試工具函數
    symbol = 'ETHUSDT'
    timeframe = '5分鐘'

    # 使用工具函數
    results = get_kline_and_calculate_indicators(timeframe, symbol)
    print("技術指標計算結果（使用工具函數）：")
    pprint(f"指數移動平均線 (EMA): {results['ema']}")

    # 或者使用兼容性類
    indicators = TechnicalIndicators(symbol, timeframe)
    results_compat = indicators.get_kline_and_calculate_indicators()
    print("\n技術指標計算結果（使用兼容性類）：")
    pprint(f"指數移動平均線 (EMA): {results_compat['ema']}")