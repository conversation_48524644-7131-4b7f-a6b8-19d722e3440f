"""
AI Trade - 增強版 LangChain 交易機器人

這是一個使用 LangChain 構建的智能交易機器人，具備：
- 完整的記憶管理系統
- 高級對話歷史管理  
- 增強版工具系統
- 專業級提示工程
- 決策追蹤和學習能力
"""

__version__ = "2.0.0"
__author__ = "Enhanced LangChain Trading Bot"

# 導入主要組件
from .core import *
from .agents import *
from .tools import *

# 主要入口點
from .enhanced_main import EnhancedTradingBot

__all__ = [
    'EnhancedTradingBot',
    'AdvancedTradingAgent',
    'TradingMemoryManager',
    'enhanced_get_market_data',
    'enhanced_execute_buy_order',
    'enhanced_execute_sell_order'
]
