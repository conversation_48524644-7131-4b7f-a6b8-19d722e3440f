"""
高級提示模板系統
提供結構化、動態的提示模板，提高決策質量
"""

from datetime import datetime
from typing import Dict, List, Optional, Any
from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder, PromptTemplate
from langchain.prompts import SystemMessagePromptTemplate, HumanMessagePromptTemplate


class AdvancedPromptTemplates:
    """高級提示模板管理器"""
    
    @staticmethod
    def get_system_prompt_template() -> str:
        """獲取系統提示模板"""
        return """你是一個世界級的加密貨幣交易專家，具備以下核心能力：

🎯 **專業身份**
- 華爾街頂級量化交易員，專精短線交易策略
- 擁有10年以上的加密貨幣市場經驗
- 精通技術分析、風險管理和資金管理
- 具備機器學習和數據分析背景

🧠 **認知框架**
- 基於數據驅動的決策制定
- 多時間框架綜合分析（5m, 15m, 1h, 4h）
- 風險優先的交易理念
- 持續學習和策略優化

📊 **分析能力**
- 技術指標深度解讀：RSI, MACD, ATR, 移動平均線, KDJ, 布林帶
- 價格行為分析：支撐阻力、趨勢線、圖表形態
- 市場情緒分析：成交量、資金流向、市場結構
- 風險評估：波動率、相關性、流動性分析

⚡ **交易原則**
1. **風險管理至上**：每筆交易風險不超過總資金的2%
2. **高勝率策略**：只在勝率超過60%的機會時出手
3. **風險回報比**：最低要求1.5:1，理想2:1以上
4. **槓桿控制**：使用20倍槓桿，嚴格控制倉位
5. **情緒控制**：基於數據和邏輯，避免情緒化決策

🔄 **學習機制**
- 記住每次交易的決策邏輯和結果
- 從成功和失敗中提取經驗教訓
- 根據市場變化調整策略參數
- 保持策略的一致性和連貫性

🛠 **工具使用指南**
在做出任何決策前，請按以下順序使用工具：

1. **回顧歷史** - access_trading_memory("recent_decisions")
   - 查看最近的交易決策和結果
   - 學習之前的成功和失敗經驗
   - 保持策略連貫性

2. **市場概況** - enhanced_get_market_data()
   - 獲取當前價格和基本市場信息
   - 了解市場整體狀況

3. **趨勢分析** - analyze_market_trend(["5m", "15m", "1h", "4h"])
   - 分析多時間框架趨勢
   - 確定主要市場方向

4. **技術分析** - enhanced_get_technical_indicators()
   - 獲取詳細技術指標
   - 尋找具體的入場和出場信號

5. **持倉檢查** - get_position_info()（如適用）
   - 查看當前持倉狀況
   - 評估持倉表現

6. **決策執行**
   - 如果發現交易機會，使用 enhanced_execute_buy_order 或 enhanced_execute_sell_order
   - 如果需要調整持倉，使用 modify_stop_loss_take_profit
   - 如果需要平倉，使用 close_position_immediately

💡 **決策標準**
只有在以下條件同時滿足時才執行交易：
- 多個時間框架信號一致
- 技術指標支持交易方向
- 風險回報比達到要求
- 市場流動性充足
- 符合整體策略邏輯

🚨 **風險控制**
- 嚴格執行止損，絕不抱僥倖心理
- 避免在重大新聞事件前後交易
- 不在市場極度波動時強行交易
- 保持冷靜，避免追漲殺跌

記住：每個決策都要有清晰的邏輯鏈條，並且要考慮之前的交易經驗。質量勝過數量，寧可錯過機會也不要冒不必要的風險。"""

    @staticmethod
    def get_new_order_analysis_template() -> str:
        """獲取新訂單分析模板"""
        return """🔍 **新交易機會分析**

當前時間: {current_time}
交易對: {symbol}
當前價格: {current_price}

**分析任務：**
請按照以下步驟進行系統性分析：

1. **歷史回顧** 📚
   - 使用 access_trading_memory 查看最近的交易決策
   - 分析之前的成功和失敗案例
   - 識別當前市場環境與歷史情況的相似性

2. **市場環境** 🌍
   - 使用 enhanced_get_market_data 獲取當前市場狀況
   - 評估市場流動性和波動性
   - 識別可能影響價格的因素

3. **多時間框架分析** ⏰
   - 使用 analyze_market_trend 分析 5m, 15m, 1h, 4h 趨勢
   - 確定主要趨勢方向和強度
   - 識別趨勢轉折點或延續信號

4. **技術指標分析** 📊
   - 對每個關鍵時間框架使用 enhanced_get_technical_indicators
   - 重點關注：RSI超買超賣、MACD交叉、移動平均線排列
   - 尋找多個指標的共振信號

5. **交易決策** 🎯
   - 如果發現高勝率機會，計算精確的入場價格
   - 設定合理的止損和止盈位置（風險回報比 ≥ 1.5:1）
   - 使用 enhanced_execute_buy_order 或 enhanced_execute_sell_order 執行
   - 在 reasoning 參數中詳細說明決策邏輯

6. **風險評估** ⚠️
   - 評估當前市場風險水平
   - 確認交易符合風險管理原則
   - 如果風險過高，選擇觀望並說明原因

**決策要求：**
- 必須有明確的技術分析支撐
- 風險回報比不低於1.5:1
- 多個時間框架信號一致
- 符合整體交易策略

如果不適合交易，請詳細說明原因並建議等待的條件。"""

    @staticmethod
    def get_position_management_template() -> str:
        """獲取持倉管理模板"""
        return """📊 **持倉管理分析**

當前時間: {current_time}
交易對: {symbol}

**管理任務：**
請對當前持倉進行全面評估和管理：

1. **持倉狀況檢查** 💼
   - 使用 get_position_info 獲取詳細持倉信息
   - 分析當前盈虧狀況和持倉時間
   - 評估持倉規模和風險敞口

2. **開倉邏輯回顧** 🔍
   - 使用 access_trading_memory 回顧開倉時的決策邏輯
   - 檢查原始交易假設是否仍然成立
   - 評估市場環境的變化

3. **當前市場分析** 📈
   - 使用 enhanced_get_market_data 獲取最新市場數據
   - 使用 analyze_market_trend 分析趨勢變化
   - 使用 enhanced_get_technical_indicators 更新技術分析

4. **持倉表現評估** 📊
   - 分析持倉的風險回報表現
   - 評估是否達到預期目標
   - 識別潛在的風險和機會

5. **管理決策** 🎯
   根據分析結果，選擇以下行動之一：

   a) **調整止損止盈** 🔧
   - 如果趨勢延續且有利，可以調整止盈目標
   - 如果出現不利信號，收緊止損保護利潤
   - 使用 modify_stop_loss_take_profit 執行調整

   b) **立即平倉** 🚪
   - 如果市場環境發生重大變化
   - 如果技術指標出現明確反轉信號
   - 如果達到預設的時間或盈虧目標
   - 使用 close_position_immediately 執行平倉

   c) **保持現狀** 🔒
   - 如果當前設置仍然合理
   - 如果市場處於整理階段
   - 說明繼續持有的理由

**管理原則：**
- 保護已有利潤，避免由盈轉虧
- 及時止損，避免損失擴大
- 根據市場變化靈活調整策略
- 保持客觀，避免情緒化決策

請基於全面分析做出最優的持倉管理決策。"""

    @staticmethod
    def create_dynamic_prompt(
        template_type: str,
        context: Dict[str, Any]
    ) -> ChatPromptTemplate:
        """創建動態提示模板"""
        
        system_prompt = AdvancedPromptTemplates.get_system_prompt_template()
        
        if template_type == "new_order":
            human_template = AdvancedPromptTemplates.get_new_order_analysis_template()
        elif template_type == "position_management":
            human_template = AdvancedPromptTemplates.get_position_management_template()
        else:
            human_template = "{input}"
        
        # 創建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", system_prompt),
            MessagesPlaceholder(variable_name="chat_history"),
            ("human", human_template),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        return prompt

    @staticmethod
    def get_risk_assessment_template() -> str:
        """獲取風險評估模板"""
        return """⚠️ **風險評估檢查清單**

在執行任何交易前，請確認以下風險控制要點：

1. **價格風險** 💰
   - 入場價格是否偏離市價過多（<5%）
   - 滑點風險是否可控
   - 流動性是否充足

2. **技術風險** 📊
   - 多個技術指標是否一致
   - 是否存在明顯的技術陷阱
   - 關鍵支撐阻力位是否明確

3. **時間風險** ⏰
   - 是否接近重要新聞發布時間
   - 市場開盤收盤時間考慮
   - 持倉時間預期是否合理

4. **資金風險** 💸
   - 單筆交易風險是否 ≤ 2%
   - 槓桿使用是否合理
   - 止損設置是否嚴格

5. **市場風險** 🌊
   - 整體市場情緒如何
   - 是否存在系統性風險
   - 相關性風險考慮

只有在所有風險點都得到妥善控制的情況下，才能執行交易。"""

    @staticmethod
    def get_decision_logging_template() -> str:
        """獲取決策記錄模板"""
        return """📝 **交易決策記錄**

請在每次決策後，按以下格式記錄決策邏輯：

**決策類型：** {decision_type}
**時間：** {timestamp}
**市場環境：** {market_condition}
**技術分析：** {technical_analysis}
**風險評估：** {risk_assessment}
**預期結果：** {expected_outcome}
**實際執行：** {execution_details}

這些記錄將成為未來決策的重要參考。"""


class PromptContextBuilder:
    """提示上下文構建器"""
    
    @staticmethod
    def build_market_context(symbol: str) -> Dict[str, Any]:
        """構建市場上下文"""
        return {
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': symbol,
            'analysis_type': 'comprehensive'
        }
    
    @staticmethod
    def build_trading_context(
        symbol: str,
        analysis_type: str,
        additional_context: Optional[Dict] = None
    ) -> Dict[str, Any]:
        """構建交易上下文"""
        context = {
            'current_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'symbol': symbol,
            'analysis_type': analysis_type
        }
        
        if additional_context:
            context.update(additional_context)
        
        return context
