# 快速開始指南

## 🚀 5分鐘快速啟動

### 1. 檢查環境配置
```bash
python main.py --check
```

如果看到所有環境變量都顯示 ✅，說明配置正確。

### 2. 啟動交易機器人
```bash
python main.py
```

### 3. 查看幫助信息
```bash
python main.py --help
```

## 📝 .env 文件配置

程序會自動創建 `.env` 模板文件，您只需要填入實際的 API 密鑰：

```bash
# Google AI API Key (用於 Gemini 模型)
GOOGLE_API_KEY=your_actual_google_api_key

# Binance API 配置
binance_api=your_actual_binance_api_key
binance_key=your_actual_binance_secret_key

# 可選配置
TRADING_SYMBOL=ETHUSDT
ANALYSIS_INTERVAL=150
MODEL_NAME=gemini-2.5-flash-lite-preview-06-17
MAX_CONSECUTIVE_ERRORS=3
```

## 🎯 主要功能

### 環境檢查
- 自動檢查所有必要的環境變量
- 顯示 API 密鑰狀態（隱藏敏感信息）
- 創建 .env 模板文件

### 智能交易
- 使用最新的 Gemini 2.5 Flash 模型
- 完整的記憶管理系統
- 決策追蹤和學習能力
- 專業級風險管理

### 錯誤處理
- 自動重試機制
- 連續錯誤限制
- 詳細的錯誤日誌

## ⚙️ 配置選項

| 環境變量 | 說明 | 默認值 | 必需 |
|---------|------|--------|------|
| GOOGLE_API_KEY | Google AI API 密鑰 | - | ✅ |
| binance_api | Binance API 密鑰 | - | ✅ |
| binance_key | Binance API 密鑰 | - | ✅ |
| TRADING_SYMBOL | 交易對 | ETHUSDT | ❌ |
| ANALYSIS_INTERVAL | 分析間隔（秒） | 150 | ❌ |
| MODEL_NAME | AI 模型名稱 | gemini-2.5-flash-lite-preview-06-17 | ❌ |
| MAX_CONSECUTIVE_ERRORS | 最大連續錯誤次數 | 3 | ❌ |

## 🛡️ 安全提醒

1. **API 密鑰安全**
   - 不要將 .env 文件提交到版本控制
   - 定期更換 API 密鑰
   - 設置適當的 API 權限

2. **交易風險**
   - 建議先在測試網測試
   - 單筆交易風險不超過總資金的2%
   - 定期檢查交易結果

3. **系統監控**
   - 監控程序運行狀態
   - 檢查錯誤日誌
   - 關注市場異常情況

## 🔧 故障排除

### 常見問題

#### 1. 環境變量未設置
```bash
❌ GOOGLE_API_KEY: 未設置
```
**解決方案**: 編輯 .env 文件，填入正確的 API 密鑰

#### 2. 依賴庫缺失
```bash
❌ 導入 EnhancedTradingBot 失敗: No module named 'xxx'
```
**解決方案**: 安裝缺失的依賴
```bash
pip install -r requirements.txt
```

#### 3. 網絡連接問題
```bash
❌ 程序執行失敗: Connection error
```
**解決方案**: 檢查網絡連接和防火牆設置

## 📊 運行示例

### 正常啟動
```
🚀 AI Trade - 增強版 LangChain 交易機器人
============================================================
啟動時間: 2024-01-01 12:00:00

📄 .env 文件已存在: /path/to/.env
🔍 檢查環境變量配置...
✅ GOOGLE_API_KEY: AIzaSyAK... (用於 Gemini AI 模型)
✅ binance_api: culKrUpR... (幣安 API 密鑰)
✅ binance_key: hhK8C9rP... (幣安 API 密鑰)
✅ 所有必要的環境變量已設置

⚙️ 當前配置:
   symbol: ETHUSDT
   model_name: gemini-2.5-flash-lite-preview-06-17
   interval: 150
   max_errors: 3

🤖 正在導入交易機器人模組...
🤖 正在初始化交易機器人...
✅ 交易機器人初始化成功

📊 機器人配置:
   交易對: ETHUSDT
   AI 模型: gemini-2.5-flash-lite-preview-06-17
   分析間隔: 150 秒
   最大連續錯誤: 3 次

🎯 開始自動交易...
按 Ctrl+C 停止程序
------------------------------------------------------------
```

## 🎉 開始交易

一切配置完成後，您就可以開始使用這個強大的 AI 交易機器人了！

記住：
- 📚 先閱讀完整文檔
- 🧪 在測試環境中測試
- 💰 合理控制風險
- 📊 定期檢查結果

祝您交易順利！ 🚀
