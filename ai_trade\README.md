# 增強版 LangChain 交易機器人

一個使用 LangChain 構建的智能加密貨幣交易機器人，具備完整的記憶管理、決策追蹤和學習能力。

## 📁 項目結構

```
ai_trade/
├── README.md                    # 項目說明
├── enhanced_main.py            # 主程序入口
├── __init__.py                 # 包初始化
│
├── core/                       # 核心模組
│   ├── __init__.py
│   ├── binance_api.py         # Binance API 接口
│   └── calculate_indicators.py # 技術指標計算
│
├── agents/                     # 代理模組
│   ├── __init__.py
│   ├── advanced_langchain_agent.py  # 高級 LangChain 代理
│   └── advanced_prompts.py          # 提示模板系統
│
├── tools/                      # 工具模組
│   ├── __init__.py
│   └── enhanced_trading_tools.py    # 增強版交易工具
│
├── tests/                      # 測試模組
│   ├── __init__.py
│   └── simple_test_enhanced.py      # 系統測試
│
└── docs/                       # 文檔
    ├── ENHANCED_REFACTORING_SUMMARY.md  # 重構總結
    └── USAGE_GUIDE.md                   # 使用指南
```

## 🚀 快速開始

### 1. 環境配置

```bash
# 安裝依賴
pip install langchain langchain-google-genai langchain-core
pip install python-binance talib numpy pandas apscheduler

# 設置環境變量
export GOOGLE_API_KEY="your_google_api_key"
export binance_api="your_binance_api_key"  
export binance_key="your_binance_secret_key"
```

### 2. 運行交易機器人

```python
from ai_trade import EnhancedTradingBot

# 創建並啟動交易機器人
bot = EnhancedTradingBot(symbol='ETHUSDT')
bot.start_trading(interval_seconds=150)
```

### 3. 手動使用代理

```python
from ai_trade.agents import AdvancedTradingAgent

# 創建代理
agent = AdvancedTradingAgent(symbol='ETHUSDT')

# 執行分析
result = agent.execute_trading_analysis("new_order")
print(result)
```

## 🧠 核心功能

### 記憶管理系統
- 持久化交易決策歷史
- 從成功和失敗中學習
- 保持策略連貫性

### 高級對話歷史
- 維護完整的決策上下文
- 自動總結長期對話
- 智能記憶管理

### 增強版工具系統
- 全面的參數驗證
- 風險回報比自動計算
- 詳細的執行日誌

### 專業提示工程
- 華爾街交易員級別設定
- 結構化決策流程
- 動態提示生成

## 🔧 模組說明

### Core 模組
- `binance_api.py`: Binance API 封裝，提供市場數據和交易執行
- `calculate_indicators.py`: 技術指標計算，支持 RSI、MACD、ATR 等

### Agents 模組  
- `advanced_langchain_agent.py`: 核心交易代理，整合記憶和決策系統
- `advanced_prompts.py`: 專業提示模板，提供結構化的交易指導

### Tools 模組
- `enhanced_trading_tools.py`: 增強版交易工具，包含參數驗證和風險管理

### Tests 模組
- `simple_test_enhanced.py`: 系統功能測試

## 📊 使用示例

### 基本交易分析
```python
from ai_trade.agents import AdvancedTradingAgent

agent = AdvancedTradingAgent(symbol='ETHUSDT')

# 新訂單分析
result = agent.execute_trading_analysis("new_order")

# 持倉管理
result = agent.execute_trading_analysis("position_management")
```

### 記憶系統操作
```python
# 查看交易記憶
memory_summary = agent.get_memory_summary()

# 獲取對話歷史
history = agent.get_conversation_history()

# 清除歷史（如需要）
agent.clear_conversation_history()
```

### 直接使用工具
```python
from ai_trade.tools import enhanced_get_market_data, enhanced_execute_buy_order

# 獲取市場數據
market_data = enhanced_get_market_data('ETHUSDT')

# 執行交易（謹慎使用）
# result = enhanced_execute_buy_order(
#     price=3000.0,
#     stop_loss_price=2950.0, 
#     take_profit_price=3100.0,
#     reasoning="技術分析顯示突破信號"
# )
```

## 🧪 測試

```bash
# 運行系統測試
cd ai_trade
python tests/simple_test_enhanced.py
```

## 📚 文檔

- [重構總結](docs/ENHANCED_REFACTORING_SUMMARY.md) - 詳細的重構說明
- [使用指南](docs/USAGE_GUIDE.md) - 完整的使用教程

## ⚠️ 重要提醒

1. **風險管理**: 請在測試環境中充分測試後再用於實盤交易
2. **API 安全**: 妥善保管 API 密鑰，設置適當的權限
3. **資金管理**: 建議單筆交易風險不超過總資金的2%
4. **監控**: 定期檢查系統運行狀態和交易結果

## 🔄 版本信息

- **版本**: 2.0.0
- **更新**: 完全重構，充分利用 LangChain 功能
- **特色**: 記憶管理、決策追蹤、策略學習

## 📞 支持

如遇問題請：
1. 查看文檔目錄中的詳細指南
2. 運行測試腳本檢查系統狀態
3. 檢查日誌文件獲取錯誤信息
