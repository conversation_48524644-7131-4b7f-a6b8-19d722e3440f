import requests
import json

class sendToLine:
    def __init__(self):
        self.headers = {'Authorization': 'Bearer BeOVpsq2vJmWit26NKasUtZjESjAOgaf5HWmJPkCFZMPilx1Q/VHCT5ZLxuSB93AdzD5HD4DX/7UImp4mcINCKOPR9LD4pela4hWV0b5gFiWZJ3cMzCL2v461wl0CuOwPPw1KyU70FgWyWKDKtckggdB04t89/1O/w1cDnyilFU=',
                        'Content-Type': 'application/json'}
        self.body = {
            'to': 'U33dcbe139d669eb71298956ab6e24a08', # line group id
            'messages': [{
                'type': 'text',
                'text': '',
            }]
        }
        
    def push_msg(self,msg):
        print("傳送訊息中...")
        if type(msg) == str:
            self.body['messages'][0]['text'] = msg
        else:
            msg.pop('action')
        self.body['messages'][0]['text'] = str(msg)
        requests.post('https://api.line.me/v2/bot/message/push',
                      headers=self.headers, data=json.dumps(self.body).encode('utf-8'))
        print("訊息傳送完成")
    
        
if __name__ == "__main__":  
    sendToLine().push_msg('hello')
