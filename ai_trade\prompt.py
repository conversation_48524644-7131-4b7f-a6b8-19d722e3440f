from binance_api import BinanceAPI
import json
from datetime import datetime

class Prompt:
# 初始化幣安API
    def __init__(self):
        self.binance = BinanceAPI(symbol='ETHUSDT')
        self.file_path = 'order_record.json'

        # 查詢歷史K線資訊
    def get_prompt_for_new_order(self):
        print("查詢歷史K線資訊...")
        #kline_info_1m = self.binance.get_kline_1m()  # 返回K線資訊
        kline_info_15m = self.binance.get_kline_15m()  # 返回K線資訊
        kline_info_1h = self.binance.get_kline_1h()  # 返回K線資訊
        # 查詢當前價格
        print("查詢當前價格...")
        price_info = self.binance.get_price()  # 返回價格資訊
        print(f"當前價格資訊：{price_info.get('price')}")

        # 準備詢問Gemini的提示
        
        prompt = (
            "你是一名來自華爾街的高勝率交易員，現在你需要針對市場行情進行判斷，並且展示你的能力使你的投資人願意加大投資你的力度。"
            "希望你在盡可能縮小損失的同時盡可能地擴大收益，並且考慮到手續費問題，交易頻率越低越好。"
            "只有當你認為市場出現了極高勝率且收益潛力超過10%的機會時，才應該進行交易，否則應保持觀望。"
            "你的目標是用最少的交易次數實現最大的收益，避免頻繁交易帶來的手續費和市場波動風險。"
        )

        prompt += (
            "請以長期投資的視角對市場進行分析，尋找能夠穩健帶來收益的交易機會，而不是追求短期的小幅波動。"
            "所有交易策略應基於確定性極高的趨勢，並以最少的交易次數實現收益最大化。"
            "請你根據以下我給你的資訊做出對於市場行情的判斷\n"
        )

        prompt += (
            "注意：止損價格應設置在合理的範圍內，以最大程度減少損失且符合交易邏輯。\n"
            "在沒有盈利需要保護本金的情況下：\n"
            "如果是做多（BUY），止損價格應低於當前市場價格的一定幅度，"
            "避免設置得過低。\n"
            "如果是做空（SELL），止損價格應高於當前市場價格的一定幅度。\n"
            "請在回覆中檢查止損價格是否符合這一規則。\n"
        )


        prompt += (
            f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            f"當前價格資訊：\n"
            f"  - 交易對: {price_info['symbol']}\n"
            f"  - 當前價格: {"{:.1f}".format(float(price_info['price']))}\n\n"
            f"以下為最近的K線資訊（每根K線代表1分鐘）：\n"
        )

        for kline in kline_info_1m:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )

        prompt += (

            f"以下為最近的K線資訊（每根K線代表15分鐘）：\n"
        )  

        for kline in kline_info_15m:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )
        
        prompt += (

            f"以下為最近的K線資訊（每根K線代表1小時）：\n"
        )
        
        for kline in kline_info_1h:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )

        prompt += (
            "請問目前是否有交易機會？請嚴格遵守以下格式回覆：\n"
            '{"交易機會": "是/否",'
            '"action": "BUY/SELL(做多請回覆BUY，做空請回覆SELL)/null(若無交易機會則回覆null)",'
            '"建議價格": 價格(int),'
            '"止損價格": 價格(int),'
            '"止盈價格": 價格(int),'
            '"交易理由": "說明為何選擇交易"}\n'
        )
        prompt += (
            "此外，如果你發現市場存在明顯的反轉信號，並且繼續持倉可能導致潛在的虧損，"
            "即使收益未達到原預期，也應選擇即時止盈，以避免進一步損失。\n"
            "在此情況下，應綜合考慮市場趨勢、成交量變化及其它相關指標進行判斷，"
            "並在回覆中詳細說明選擇即時止盈的理由。\n"
        )
        return prompt
    
    def get_prompt_for_stop_loss_or_take_profit(self, order_record):
        # if order_record.get('main_order') == None:
        #     with open(self.file_path, 'r', encoding='utf-8') as file:
        #         try:
        #             data = json.load(file)
        #             order_record['main_order'] = data.get('main_order')
        #         except json.JSONDecodeError as e:
        #             print(f"讀取訂單記錄時出錯: {e}")
        #             return None
        print("查詢歷史K線資訊...")
        #kline_info_1m = self.binance.get_kline_1m()  # 返回K線資訊
        kline_info_15m = self.binance.get_kline_15m()  # 返回K線資訊
        kline_info_1h = self.binance.get_kline_1h()  # 返回K線資訊
        # 查詢當前價格
        print("查詢當前價格...")
        price_info = self.binance.get_price()  # 返回價格資訊
        print(f"當前價格資訊：{price_info.get('price')}")

        prompt = (
            "你是一名來自華爾街的高勝率交易員，現在你需要針對市場行情進行判斷，並且展示你的能力使你的投資人願意加大投資你的力度。"
            "希望你在盡可能縮小損失的同時盡可能地擴大收益，並且考慮到手續費問題，交易頻率越低越好。"
            "只有當你認為市場出現了極高勝率且收益潛力超過10%的機會時，才應該進行交易，否則應保持觀望。"
            "你的目標是用最少的交易次數實現最大的收益，避免頻繁交易帶來的手續費和市場波動風險。"
        )

        prompt += (
            "請以長期投資的視角對市場進行分析，尋找能夠穩健帶來收益的交易機會，而不是追求短期的小幅波動。"
            "所有交易策略應基於確定性極高的趨勢，並以最少的交易次數實現收益最大化。"
            "請你根據以下我給你的資訊做出對於市場行情的判斷\n"
        )

        prompt += (
            f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            f"當前價格資訊：\n"
            f"  - 交易對: {price_info['symbol']}\n"
            f"  - 當前價格: {"{:.1f}".format(float(price_info['price']))}\n\n"
            f"以下為最近的K線資訊（每根K線代表1分鐘）：\n"
        )

        for kline in kline_info_1m:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )

        prompt += (

            f"以下為最近的K線資訊（每根K線代表15分鐘）：\n"
        )

        for kline in kline_info_15m:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )
        
        prompt += (

            f"以下為最近的K線資訊（每根K線代表1小時）：\n"
        )
        
        for kline in kline_info_1h:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )
        
        main_order_updateTime = order_record.get('main_order').get('updateTime') / 1000
        prompt += (
            "當前訂單資訊為：\n"
            f"  - 當前訂單開單時間為：{datetime.fromtimestamp(main_order_updateTime ).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 當前訂單為：{"做多" if order_record.get('main_order').get('side') == "BUY" else "做空"}\n"
            f"  - 主訂單：{order_record.get('main_order').get('price')}\n"
            f"  - 止損訂單：{order_record.get('stop_loss_order').get('stopPrice')}\n"
            f"  - 止盈訂單：{order_record.get('take_profit_order').get('stopPrice')}\n"
        )

        prompt += (
            "請問目前是需要提前止盈止損，或是修改止盈止損價格？\n"
            "若是你認為應當提前以市價止盈止損，請嚴格遵守以下格式回覆：\n"
            '{"action": "STOP_LOSS_TAKE_PROFIT",'
            '"理由": "說明你對當前市場狀態的分析"}\n'
            "若是你認為應當修改止盈止損價格，請嚴格遵守以下格式回覆：\n"
            '{"action": "MODIFY_STOP_LOSS_TAKE_PROFIT",'
            '"止損價格": 價格(int),'
            '"止盈價格": 價格(int),'
            '"交易理由": "說明為何選擇交易"}\n'
            "若是你認為當前訂單架和無須更動，請嚴格遵守以下格式回覆：\n"
            '{"action": null,'
            '"理由": "說明你對當前市場狀態的分析"}\n'
        )
        prompt += (
                "如果你認為市場趨勢即將出現明顯的反轉，並且當前持倉的繼續存在可能導致潛在虧損，"
                "即使當前收益未達到預期，也應果斷選擇即時止盈，以確保避免進一步損失。\n"
                "請在回覆中說明你為何判斷需要即時止盈，並提供相關市場分析作為支撐。\n"
            )

        prompt += (
            f"注意：止損價格應設置在合理的範圍內(當前市場價格為{"{:.1f}".format(float(price_info['price']))})，以最大程度減少損失且符合交易邏輯。\n"
            "在沒有盈利需要保護本金的情況下：\n"
            "如果是做多（BUY），止損價格應低於當前市場價格的一定幅度，"
            "避免設置得過低。\n"
            "如果是做空（SELL），止損價格應高於當前市場價格的一定幅度。\n"
            "請在回覆中檢查止損價格是否符合這一規則。\n"
            "請在回覆中檢查是否符合回覆格式。\n"
        )
        return prompt       

    def get_prompt_for_cancel_order(self, order_record):
        print("查詢歷史K線資訊...")
        #kline_info_1m = self.binance.get_kline_1m() 
        kline_info_15m = self.binance.get_kline_15m()  # 返回K線資訊
        kline_info_1h = self.binance.get_kline_1h()  # 返回K線資訊
        # 查詢當前價格
        print("查詢當前價格...")
        price_info = self.binance.get_price()  # 返回價格資訊
        print(f"當前價格資訊：{price_info.get('price')}")

        prompt = (
            "你是一名來自華爾街的高勝率交易員，現在你需要針對市場行情進行判斷，並且展示你的能力使你的投資人願意加大投資你的力度。"
            "希望你在盡可能縮小損失的同時盡可能地擴大收益，並且考慮到手續費問題，交易頻率越低越好。"
            "只有當你認為市場出現了極高勝率且收益潛力超過10%的機會時，才應該進行交易，否則應保持觀望。"
            "你的目標是用最少的交易次數實現最大的收益，避免頻繁交易帶來的手續費和市場波動風險。"
        )

        prompt += (
            "請你根據以下我給你的資訊做出對於市場行情的判斷\n"
        )

        prompt += (
            f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n"
            f"當前價格資訊：\n"
            f"  - 交易對: {price_info['symbol']}\n"
            f"  - 當前價格: {"{:.1f}".format(float(price_info['price']))}\n\n"
            f"以下為最近的K線資訊（每根K線代表1分鐘）：\n"
        )

        for kline in kline_info_1m:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )     

        prompt += (

            f"以下為最近的K線資訊（每根K線代表15分鐘）：\n"
        )

        for kline in kline_info_15m:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )
        
        prompt += (

            f"以下為最近的K線資訊（每根K線代表1小時）：\n"
        )
        
        for kline in kline_info_1h:
            timestamp_s = kline[0] / 1000
            prompt += (
                f'{{"開盤時間": {datetime.fromtimestamp(timestamp_s).strftime('%Y-%m-%d %H:%M:%S')}, '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )
        
        main_order_updateTime = order_record.get('main_order').get('updateTime') / 1000
        prompt += (
            "當前訂單資訊為：\n"
            f"  - 當前訂單開單時間為：{datetime.fromtimestamp(main_order_updateTime ).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 當前訂單為：{"做多" if order_record.get('main_order').get('side') == "BUY" else "做空"}\n"
            f"  - 主訂單：{order_record.get('main_order').get('price')}\n"
            f"  - 止損訂單：{order_record.get('stop_loss_order').get('stopPrice')}\n"
            f"  - 止盈訂單：{order_record.get('take_profit_order').get('stopPrice')}\n"
        )

        prompt += (
            "請問目前是否需要取消這些未成立訂單(訂單未成立還沒有扣除手續費)？請嚴格遵守以下格式回覆：\n"
            '{"action": "CANCEL_ORDER/KEEP_ORDER",'
            '"交易理由": "說明為何選擇交易"}\n'
        )
        prompt += (
            "如果你認為市場已顯示出明顯的反轉信號，並且繼續持倉可能導致虧損，"
            "則應優先考慮取消訂單並止盈退出，"
            "即使未達到初始設定的目標收益，也需將損失控制在最小範圍內。\n"
            "請結合當前市場數據詳細說明你為何選擇取消訂單，並在回覆中提供相關的分析依據。\n"
        )

        prompt += (
            f"注意：止損價格應設置在合理的範圍內(當前市場價格為{"{:.1f}".format(float(price_info['price']))})，以最大程度減少損失且符合交易邏輯。\n"
            "在沒有盈利需要保護本金的情況下：\n"
            "如果是做多（BUY），止損價格應低於當前市場價格的一定幅度，"
            "避免設置得過低。\n"
            "如果是做空（SELL），止損價格應高於當前市場價格的一定幅度。\n"
            "請在回覆中檢查止損價格是否符合這一規則。\n"
            "請在回覆中檢查是否符合回覆格式。\n"
        )
        return prompt

