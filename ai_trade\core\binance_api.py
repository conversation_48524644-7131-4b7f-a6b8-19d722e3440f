from binance.client import Client
from binance.enums import (
    KLINE_INTERVAL_1MINUTE, KLINE_INTERVAL_5MINUTE, KLINE_INTERVAL_15MINUTE,
    KLINE_INTERVAL_1HOUR, KLINE_INTERVAL_4HOUR, KLINE_INTERVAL_1DAY,
    SIDE_BUY, SIDE_SELL, ORDER_TYPE_LIMIT, ORDER_TYPE_MARKET,
    TIME_IN_FORCE_GTC
)
from binance.exceptions import BinanceAPIException, BinanceOrderException
import os
from pprint import pprint
import json
from datetime import datetime, timedelta
from langchain_core.tools import tool
from typing import Dict, List, Optional, Union

# 全局 Binance 客戶端實例
_binance_client = None
_default_symbol = 'ETHUSDT'

def get_binance_client():
    """獲取 Binance 客戶端實例"""
    global _binance_client
    if _binance_client is None:
        api_key = os.environ.get('binance_api')
        api_secret = os.environ.get('binance_key')
        _binance_client = Client(api_key, api_secret)
    return _binance_client

def set_default_symbol(symbol: str):
    """設置默認交易對"""
    global _default_symbol
    _default_symbol = symbol

def get_default_symbol():
    """獲取默認交易對"""
    return _default_symbol

@tool
def get_price(symbol: Optional[str] = None) -> Dict:
    """獲取指定交易對的當前價格資訊

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含價格資訊的字典
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    price_info = client.futures_symbol_ticker(symbol=symbol)
    return price_info

@tool
def get_kline_1m(symbol: Optional[str] = None) -> List:
    """獲取1分鐘K線數據

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        K線數據列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    klines = client.futures_klines(symbol=symbol, interval=KLINE_INTERVAL_1MINUTE)
    return klines

@tool
def get_kline_5m(symbol: Optional[str] = None) -> List:
    """獲取5分鐘K線數據

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        K線數據列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    klines = client.futures_klines(symbol=symbol, interval=KLINE_INTERVAL_5MINUTE)
    return klines

@tool
def get_kline_15m(symbol: Optional[str] = None) -> List:
    """獲取15分鐘K線數據

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        K線數據列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    klines = client.futures_klines(symbol=symbol, interval=KLINE_INTERVAL_15MINUTE)
    return klines

@tool
def get_kline_1h(symbol: Optional[str] = None) -> List:
    """獲取1小時K線數據

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        K線數據列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    klines = client.futures_klines(symbol=symbol, interval=KLINE_INTERVAL_1HOUR)
    return klines

@tool
def get_kline_4h(symbol: Optional[str] = None) -> List:
    """獲取4小時K線數據

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        K線數據列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    klines = client.futures_klines(symbol=symbol, interval=KLINE_INTERVAL_4HOUR)
    return klines

@tool
def get_kline_1d(symbol: Optional[str] = None) -> List:
    """獲取1天K線數據

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        K線數據列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    klines = client.futures_klines(symbol=symbol, interval=KLINE_INTERVAL_1DAY)
    return klines

@tool
def get_order_book(symbol: Optional[str] = None) -> Dict:
    """獲取訂單簿資訊

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含買單和賣單資訊的字典
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    depth = client.get_order_book(symbol=symbol)
    return {
        "bids": depth['bids'][:10],
        "asks": depth['asks'][:10]
    }

@tool
def create_side_buy_order(quantity: float, price: float, symbol: Optional[str] = None) -> Optional[Dict]:
    """創建限價買單

    Args:
        quantity: 交易數量
        price: 限價價格
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        訂單資訊字典，如果失敗則返回 None
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    try:
        order = client.order_limit_buy(
            symbol=symbol,
            quantity=quantity,
            price=price)
        return order
    except BinanceAPIException as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print(f"API Exception: {e}")
        return None
    except BinanceOrderException as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print(f"Order Exception: {e}")
        return None

@tool
def create_futures_order(
    side: str,
    order_type: str,
    quantity: float,
    price: float,
    time_in_force: str,
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None
) -> tuple:
    """創建合約交易訂單並設置止損和止盈

    Args:
        side: 交易方向 ('BUY' 或 'SELL')
        order_type: 訂單類型
        quantity: 交易數量
        price: 價格
        time_in_force: 時間有效性
        stop_loss_price: 止損價格
        take_profit_price: 止盈價格
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含主訂單、止損訂單和止盈訂單的元組
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()

    try:
        # 創建主訂單
        order = client.futures_create_order(
            symbol=symbol,
            side=side,
            type=order_type,
            quantity=quantity,
            price=price,
            timeInForce=time_in_force
        )

        # 設置止損訂單
        stop_loss_order = client.futures_create_order(
            symbol=symbol,
            side="SELL" if side == "BUY" else "BUY",
            type="STOP_MARKET",
            stopPrice=stop_loss_price,
            quantity=quantity,
            reduceOnly=True
        )

        # 設置止盈訂單
        take_profit_order = client.futures_create_order(
            symbol=symbol,
            side="SELL" if side == "BUY" else "BUY",
            type="TAKE_PROFIT_MARKET",
            stopPrice=take_profit_price,
            quantity=quantity,
            reduceOnly=True
        )

        return order, stop_loss_order, take_profit_order

    except BinanceAPIException as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print(f"API Exception: {e}")
        return None, None, None
    except BinanceOrderException as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print(f"Order Exception: {e}")
        return None, None, None

@tool
def create_stop_loss_take_profit_futures_order(
    quantity: float,
    side: str,
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None
) -> tuple:
    """創建止損止盈訂單

    Args:
        quantity: 交易數量
        side: 交易方向 ('BUY' 或 'SELL')
        stop_loss_price: 止損價格
        take_profit_price: 止盈價格
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含止損訂單和止盈訂單的元組
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()

    try:
        quantity_abs = abs(float(quantity))
        # 設置止損訂單
        stop_loss_order = client.futures_create_order(
            symbol=symbol,
            side=side,
            type="STOP_MARKET",
            stopPrice=stop_loss_price,
            quantity=quantity_abs,
            reduceOnly=True
        )
        # 設置止盈訂單
        take_profit_order = client.futures_create_order(
            symbol=symbol,
            side=side,
            type="TAKE_PROFIT_MARKET",
            stopPrice=take_profit_price,
            quantity=quantity_abs,
            reduceOnly=True
        )
        return stop_loss_order, take_profit_order
    except BinanceAPIException as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        error_message = str(e)
        print(f"API Exception: {error_message}")

        if "APIError(code=-2021): Order would immediately trigger." in error_message:
            print("檢測到訂單會立即觸發，正在關閉訂單...")
            close_order(side, quantity, symbol)
        return None, None

    except BinanceOrderException as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print(f"Order Exception: {e}")
        return None, None
@tool
def get_account_info(asset: str = 'BTC') -> Dict:
    """獲取帳戶資產資訊

    Args:
        asset: 資產符號，默認為 'BTC'

    Returns:
        資產資訊字典
    """
    client = get_binance_client()
    return client.get_asset_balance(asset=asset)

@tool
def get_exchange_info(symbol: Optional[str] = None) -> Dict:
    """獲取交易所資訊

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        包含步長、價格精度等資訊的字典
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    exchange_info = client.futures_exchange_info()
    for symbol_info in exchange_info['symbols']:
        if symbol_info['symbol'] == symbol:
            step_size = symbol_info['filters'][1]['stepSize']
            tick_size = symbol_info['filters'][0]['tickSize']
            return {"step_size": step_size, "tick_size": tick_size, "minPrice": symbol_info['filters'][0]['minPrice']}
    return {}

@tool
def change_futures_leverage(leverage: int = 20, symbol: Optional[str] = None) -> None:
    """修改期貨槓桿倍數

    Args:
        leverage: 槓桿倍數，默認為 20
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    client.futures_change_leverage(symbol=symbol, leverage=leverage)

@tool
def get_trading_status(symbol: Optional[str] = None) -> List:
    """獲取當前開放訂單狀態

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        開放訂單列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    orders = client.futures_get_open_orders(symbol=symbol)
    return orders

@tool
def get_open_futures_orders(symbol: Optional[str] = None) -> List:
    """獲取期貨持倉資訊

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        持倉資訊列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    try:
        open_orders = client.futures_position_information(symbol=symbol)
        return open_orders
    except Exception as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print(f"查詢失敗: {e}")
        return []
@tool
def cancel_order(order_id: int, symbol: Optional[str] = None) -> Optional[Dict]:
    """取消特定訂單

    Args:
        order_id: 訂單ID
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        取消訂單的回應，如果失敗則返回 None
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    try:
        cancel_response = client.futures_cancel_order(symbol=symbol, orderId=order_id)
        print("取消訂單成功!")
        return cancel_response
    except Exception as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            file.write(f"API Exception: {e}\n")
        print("取消訂單失敗:", e)
        return None

@tool
def close_order(side: str, quantity: float, symbol: Optional[str] = None) -> Optional[Dict]:
    """平倉訂單

    Args:
        side: 交易方向 ('BUY' 或 'SELL')
        quantity: 交易數量
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        平倉訂單回應，如果失敗則返回 None
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    try:
        quantity_abs = abs(float(quantity))
        close_response = client.futures_create_order(
            symbol=symbol,
            side=side,
            type="MARKET",
            quantity=quantity_abs,
            reduceOnly=True
        )
        print("平倉訂單成功:", close_response)
        return close_response
    except Exception as e:
        with open('error.txt','a',encoding='utf-8') as file:
            file.write(f"平倉訂單失敗: {e}\n")
            file.write(f"side: {side}\n")
            file.write(f"quantity: {quantity}\n")
        print("平倉訂單失敗:", e)
        return None

@tool
def get_futures_order_history(symbol: Optional[str] = None, limit: int = 16, hours: int = 20) -> List:
    """獲取期貨交易歷史

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對
        limit: 限制返回的交易數量，默認為 16
        hours: 時間範圍（小時），默認為 20 小時內

    Returns:
        交易歷史列表
    """
    if symbol is None:
        symbol = get_default_symbol()
    client = get_binance_client()
    orders = []
    trades = client.futures_account_trades(symbol=symbol, limit=limit)
    now = datetime.now()
    time_threshold = now - timedelta(hours=hours)
    for trade in trades:
        trade_time = datetime.fromtimestamp(trade['time'] / 1000)
        if trade_time >= time_threshold:
            orders.append(trade)
    return orders

# 交易執行工具 - 供 LangChain Agent 直接調用
@tool
def execute_buy_order(
    price: float,
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None,
    quantity_usd: float = 20.0
) -> Dict:
    """執行買入訂單（做多）

    Args:
        price: 買入價格
        stop_loss_price: 止損價格
        take_profit_price: 止盈價格
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對
        quantity_usd: 交易金額（美元），默認 20

    Returns:
        訂單執行結果
    """
    if symbol is None:
        symbol = get_default_symbol()

    try:
        # 計算數量
        current_price = get_price(symbol)['price']
        quantity = round(quantity_usd / float(current_price), 3)

        # 執行訂單
        order, _, _ = create_futures_order(
            side="BUY",
            order_type="LIMIT",
            quantity=quantity,
            price=price,
            time_in_force="GTC",
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price,
            symbol=symbol
        )

        if order:
            return {
                "success": True,
                "action": "BUY_ORDER_EXECUTED",
                "message": f"成功執行買入訂單：價格 {price}，止損 {stop_loss_price}，止盈 {take_profit_price}",
                "order_id": order.get('orderId'),
                "quantity": quantity
            }
        else:
            return {
                "success": False,
                "action": "BUY_ORDER_FAILED",
                "message": "買入訂單執行失敗"
            }
    except Exception as e:
        return {
            "success": False,
            "action": "BUY_ORDER_ERROR",
            "message": f"買入訂單執行錯誤：{str(e)}"
        }

@tool
def execute_sell_order(
    price: float,
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None,
    quantity_usd: float = 20.0
) -> Dict:
    """執行賣出訂單（做空）

    Args:
        price: 賣出價格
        stop_loss_price: 止損價格
        take_profit_price: 止盈價格
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對
        quantity_usd: 交易金額（美元），默認 20

    Returns:
        訂單執行結果
    """
    if symbol is None:
        symbol = get_default_symbol()

    try:
        # 計算數量
        current_price = get_price(symbol)['price']
        quantity = round(quantity_usd / float(current_price), 3)

        # 執行訂單
        order, _, _ = create_futures_order(
            side="SELL",
            order_type="LIMIT",
            quantity=quantity,
            price=price,
            time_in_force="GTC",
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price,
            symbol=symbol
        )

        if order:
            return {
                "success": True,
                "action": "SELL_ORDER_EXECUTED",
                "message": f"成功執行賣出訂單：價格 {price}，止損 {stop_loss_price}，止盈 {take_profit_price}",
                "order_id": order.get('orderId'),
                "quantity": quantity
            }
        else:
            return {
                "success": False,
                "action": "SELL_ORDER_FAILED",
                "message": "賣出訂單執行失敗"
            }
    except Exception as e:
        return {
            "success": False,
            "action": "SELL_ORDER_ERROR",
            "message": f"賣出訂單執行錯誤：{str(e)}"
        }

@tool
def modify_stop_loss_take_profit(
    stop_loss_price: float,
    take_profit_price: float,
    symbol: Optional[str] = None
) -> Dict:
    """修改當前持倉的止損止盈價格

    Args:
        stop_loss_price: 新的止損價格
        take_profit_price: 新的止盈價格
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對

    Returns:
        修改結果
    """
    if symbol is None:
        symbol = get_default_symbol()

    try:
        # 獲取當前持倉
        positions = get_open_futures_orders(symbol)
        if not positions or len(positions) == 0:
            return {
                "success": False,
                "action": "NO_POSITION",
                "message": "沒有找到當前持倉"
            }

        position = positions[0]
        quantity = abs(float(position['positionAmt']))
        side = 'BUY' if float(position['positionAmt']) > 0 else 'SELL'

        # 獲取當前的止損止盈訂單並取消
        orders = get_trading_status(symbol)
        for order in orders:
            if order.get("type") in ["STOP_MARKET", "TAKE_PROFIT_MARKET"]:
                cancel_order(order['orderId'], symbol)

        # 創建新的止損止盈訂單
        stop_loss_order, take_profit_order = create_stop_loss_take_profit_futures_order(
            quantity=quantity,
            side=side,
            stop_loss_price=stop_loss_price,
            take_profit_price=take_profit_price,
            symbol=symbol
        )

        if stop_loss_order and take_profit_order:
            return {
                "success": True,
                "action": "STOP_LOSS_TAKE_PROFIT_MODIFIED",
                "message": f"成功修改止損止盈：止損 {stop_loss_price}，止盈 {take_profit_price}"
            }
        else:
            return {
                "success": False,
                "action": "MODIFY_FAILED",
                "message": "修改止損止盈失敗"
            }
    except Exception as e:
        return {
            "success": False,
            "action": "MODIFY_ERROR",
            "message": f"修改止損止盈錯誤：{str(e)}"
        }

@tool
def close_position_immediately(symbol: Optional[str] = None, reason: str = "手動平倉") -> Dict:
    """立即平倉當前持倉

    Args:
        symbol: 交易對符號，如 'ETHUSDT'。如果未提供，使用默認交易對
        reason: 平倉原因

    Returns:
        平倉結果
    """
    if symbol is None:
        symbol = get_default_symbol()

    try:
        # 獲取當前持倉
        positions = get_open_futures_orders(symbol)
        if not positions or len(positions) == 0:
            return {
                "success": False,
                "action": "NO_POSITION",
                "message": "沒有找到當前持倉"
            }

        position = positions[0]
        quantity = abs(float(position['positionAmt']))
        side = 'SELL' if float(position['positionAmt']) > 0 else 'BUY'

        # 取消所有相關訂單
        orders = get_trading_status(symbol)
        for order in orders:
            cancel_order(order['orderId'], symbol)

        # 市價平倉
        close_result = close_order(side, quantity, symbol)

        if close_result:
            return {
                "success": True,
                "action": "POSITION_CLOSED",
                "message": f"成功平倉：{reason}",
                "quantity": quantity
            }
        else:
            return {
                "success": False,
                "action": "CLOSE_FAILED",
                "message": "平倉失敗"
            }
    except Exception as e:
        return {
            "success": False,
            "action": "CLOSE_ERROR",
            "message": f"平倉錯誤：{str(e)}"
        }

# 為了向後兼容，提供一個包裝類
class BinanceAPI:
    """向後兼容的 BinanceAPI 類，內部使用 LangChain 工具"""

    def __init__(self, symbol: str):
        set_default_symbol(symbol)
        self.symbol = symbol

    def get_price(self):
        return get_price(self.symbol)

    def get_kline_1m(self):
        return get_kline_1m(self.symbol)

    def get_kline_5m(self):
        return get_kline_5m(self.symbol)

    def get_kline_15m(self):
        return get_kline_15m(self.symbol)

    def get_kline_1h(self):
        return get_kline_1h(self.symbol)

    def get_kline_4h(self):
        return get_kline_4h(self.symbol)

    def get_kline_1d(self):
        return get_kline_1d(self.symbol)

    def get_order_book(self):
        return get_order_book(self.symbol)

    def create_side_buy_order(self, quantity, price):
        return create_side_buy_order(quantity, price, self.symbol)

    def create_futures_order(self, side, order_type, quantity, price, time_in_force, stop_loss_price, take_profit_price):
        return create_futures_order(side, order_type, quantity, price, time_in_force, stop_loss_price, take_profit_price, self.symbol)

    def create_stop_loss_take_profit_futures_order(self, quantity, side, stop_loss_price, take_profit_price):
        return create_stop_loss_take_profit_futures_order(quantity, side, stop_loss_price, take_profit_price, self.symbol)

    def get_account_info(self):
        return get_account_info()

    def get_exchange_info(self):
        return get_exchange_info(self.symbol)

    def change_futures_leverage(self, leverage=20):
        return change_futures_leverage(leverage, self.symbol)

    def get_trading_status(self):
        return get_trading_status(self.symbol)

    def get_open_futures_orders(self):
        return get_open_futures_orders(self.symbol)

    def cancel_order(self, order_id):
        return cancel_order(order_id, self.symbol)

    def close_order(self, side, quantity):
        return close_order(side, quantity, self.symbol)

    def get_futures_order_history(self):
        return get_futures_order_history(self.symbol)

# 使用範例
if __name__ == "__main__":
    # 設置默認交易對
    set_default_symbol('ETHUSDT')

    # 測試工具函數
    price_info = get_price()
    print("價格資訊:", price_info)

    # 或者使用兼容性類
    binance = BinanceAPI(symbol='ETHUSDT')
    history = binance.get_futures_order_history()
    pprint(history)

