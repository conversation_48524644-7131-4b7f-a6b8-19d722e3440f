"""
測試增強版 LangChain 交易系統
驗證新架構的功能性和穩定性
"""

import sys
import os
import traceback
from datetime import datetime
from typing import Dict, Any

# 添加當前目錄到路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from advanced_langchain_agent import AdvancedTradingAgent, TradingMemoryManager
from enhanced_trading_tools import (
    enhanced_get_market_data, enhanced_get_technical_indicators,
    enhanced_execute_buy_order, enhanced_execute_sell_order,
    TradingToolsValidator
)
from enhanced_main import EnhancedTradingBot


class EnhancedSystemTester:
    """增強版系統測試器"""
    
    def __init__(self):
        self.test_results = []
        self.symbol = 'ETHUSDT'
    
    def log_test_result(self, test_name: str, success: bool, message: str = "", details: Any = None):
        """記錄測試結果"""
        result = {
            'test_name': test_name,
            'success': success,
            'message': message,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.test_results.append(result)
        
        status = "✅ 通過" if success else "❌ 失敗"
        print(f"{status} {test_name}: {message}")
        
        if details and not success:
            print(f"   詳情: {details}")
    
    def test_memory_manager(self) -> bool:
        """測試記憶管理器"""
        try:
            print("\n📝 測試記憶管理器...")
            
            # 創建記憶管理器
            memory_manager = TradingMemoryManager("test_memory.json")
            
            # 測試添加決策
            memory_manager.add_decision(
                decision_type="test_buy",
                reasoning="測試買入決策",
                market_context={"price": 3000},
                result="測試結果"
            )
            
            # 測試獲取決策
            recent_decisions = memory_manager.get_recent_decisions(1)
            
            if len(recent_decisions) > 0:
                self.log_test_result("記憶管理器", True, "成功添加和獲取決策記錄")
                return True
            else:
                self.log_test_result("記憶管理器", False, "無法獲取決策記錄")
                return False
                
        except Exception as e:
            self.log_test_result("記憶管理器", False, f"測試失敗: {str(e)}", traceback.format_exc())
            return False
    
    def test_enhanced_tools(self) -> bool:
        """測試增強版工具"""
        try:
            print("\n🛠 測試增強版工具...")
            
            # 測試市場數據工具
            market_data = enhanced_get_market_data(self.symbol)
            if 'error' not in market_data and 'price_info' in market_data:
                self.log_test_result("增強版市場數據工具", True, "成功獲取市場數據")
            else:
                self.log_test_result("增強版市場數據工具", False, "獲取市場數據失敗", market_data)
                return False
            
            # 測試技術指標工具
            indicators = enhanced_get_technical_indicators("5m", self.symbol)
            if 'error' not in indicators and 'indicators' in indicators:
                self.log_test_result("增強版技術指標工具", True, "成功獲取技術指標")
            else:
                self.log_test_result("增強版技術指標工具", False, "獲取技術指標失敗", indicators)
                return False
            
            # 測試參數驗證器
            validator = TradingToolsValidator()
            current_price = float(market_data['price_info']['price'])
            
            # 測試價格驗證
            valid_price = validator.validate_price(current_price * 1.01, self.symbol)  # 1% 偏差
            invalid_price = validator.validate_price(current_price * 1.15, self.symbol)  # 15% 偏差
            
            if valid_price and not invalid_price:
                self.log_test_result("參數驗證器", True, "價格驗證功能正常")
            else:
                self.log_test_result("參數驗證器", False, f"價格驗證異常: valid={valid_price}, invalid={invalid_price}")
                return False
            
            return True
            
        except Exception as e:
            self.log_test_result("增強版工具", False, f"測試失敗: {str(e)}", traceback.format_exc())
            return False
    
    def test_advanced_agent(self) -> bool:
        """測試高級代理"""
        try:
            print("\n🤖 測試高級代理...")
            
            # 創建代理（使用較小的模型進行測試）
            agent = AdvancedTradingAgent(
                model_name="gemini-1.5-flash-latest",
                symbol=self.symbol,
                memory_type="buffer"
            )
            
            # 測試工具設置
            tools = agent._setup_tools()
            if len(tools) >= 10:  # 應該有至少10個工具
                self.log_test_result("代理工具設置", True, f"成功設置 {len(tools)} 個工具")
            else:
                self.log_test_result("代理工具設置", False, f"工具數量不足: {len(tools)}")
                return False
            
            # 測試記憶系統
            memory_summary = agent.get_memory_summary()
            if isinstance(memory_summary, str):
                self.log_test_result("代理記憶系統", True, "記憶系統正常運行")
            else:
                self.log_test_result("代理記憶系統", False, "記憶系統異常")
                return False
            
            # 測試對話歷史
            history = agent.get_conversation_history()
            if isinstance(history, list):
                self.log_test_result("對話歷史系統", True, f"對話歷史系統正常，當前 {len(history)} 條記錄")
            else:
                self.log_test_result("對話歷史系統", False, "對話歷史系統異常")
                return False
            
            return True
            
        except Exception as e:
            self.log_test_result("高級代理", False, f"測試失敗: {str(e)}", traceback.format_exc())
            return False
    
    def test_trading_bot(self) -> bool:
        """測試交易機器人"""
        try:
            print("\n🤖 測試交易機器人...")
            
            # 創建交易機器人
            bot = EnhancedTradingBot(symbol=self.symbol)
            
            # 測試狀態獲取
            status = bot.get_current_trading_status()
            if 'error' not in status:
                self.log_test_result("交易狀態獲取", True, "成功獲取交易狀態")
            else:
                self.log_test_result("交易狀態獲取", False, "獲取交易狀態失敗", status)
                return False
            
            # 測試狀態報告
            report = bot.get_status_report()
            if "交易機器人狀態報告" in report:
                self.log_test_result("狀態報告生成", True, "成功生成狀態報告")
            else:
                self.log_test_result("狀態報告生成", False, "狀態報告生成失敗", report)
                return False
            
            return True
            
        except Exception as e:
            self.log_test_result("交易機器人", False, f"測試失敗: {str(e)}", traceback.format_exc())
            return False
    
    def test_integration(self) -> bool:
        """測試系統整合"""
        try:
            print("\n🔗 測試系統整合...")
            
            # 創建完整的交易機器人
            bot = EnhancedTradingBot(symbol=self.symbol)
            
            # 測試一個完整的分析週期（但不執行實際交易）
            print("   執行模擬分析週期...")
            
            # 獲取當前狀態
            status = bot.get_current_trading_status()
            
            # 模擬分析（不實際執行）
            if status.get('has_position', False):
                analysis_type = "position_management"
            else:
                analysis_type = "new_order"
            
            print(f"   分析類型: {analysis_type}")
            
            # 測試記憶訪問
            memory_summary = bot.agent.get_memory_summary()
            print(f"   記憶狀態: {'正常' if memory_summary else '空白'}")
            
            self.log_test_result("系統整合", True, f"整合測試完成，分析類型: {analysis_type}")
            return True
            
        except Exception as e:
            self.log_test_result("系統整合", False, f"測試失敗: {str(e)}", traceback.format_exc())
            return False
    
    def run_all_tests(self) -> bool:
        """運行所有測試"""
        print("🧪 開始增強版系統測試")
        print("=" * 60)
        
        tests = [
            ("記憶管理器", self.test_memory_manager),
            ("增強版工具", self.test_enhanced_tools),
            ("高級代理", self.test_advanced_agent),
            ("交易機器人", self.test_trading_bot),
            ("系統整合", self.test_integration)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
            except Exception as e:
                self.log_test_result(test_name, False, f"測試執行異常: {str(e)}")
        
        print("\n" + "=" * 60)
        print(f"📊 測試結果: {passed}/{total} 通過")
        
        if passed == total:
            print("🎉 所有測試通過！增強版系統運行正常！")
            return True
        else:
            print("⚠️ 部分測試失敗，請檢查相關組件")
            
            # 顯示失敗的測試
            failed_tests = [r for r in self.test_results if not r['success']]
            if failed_tests:
                print("\n❌ 失敗的測試:")
                for test in failed_tests:
                    print(f"   - {test['test_name']}: {test['message']}")
            
            return False
    
    def generate_test_report(self) -> str:
        """生成測試報告"""
        passed = sum(1 for r in self.test_results if r['success'])
        total = len(self.test_results)
        
        report = f"""
📋 增強版 LangChain 交易系統測試報告
時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
總測試數: {total}
通過測試: {passed}
失敗測試: {total - passed}
成功率: {(passed/total*100):.1f}%

詳細結果:
"""
        
        for result in self.test_results:
            status = "✅" if result['success'] else "❌"
            report += f"{status} {result['test_name']}: {result['message']}\n"
        
        return report


def main():
    """主測試函數"""
    tester = EnhancedSystemTester()
    
    try:
        success = tester.run_all_tests()
        
        # 生成測試報告
        report = tester.generate_test_report()
        print(report)
        
        # 保存測試報告
        with open("test_report_enhanced.txt", "w", encoding="utf-8") as f:
            f.write(report)
        
        return success
        
    except Exception as e:
        print(f"❌ 測試執行失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
