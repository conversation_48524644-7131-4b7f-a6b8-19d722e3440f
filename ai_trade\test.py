from prompt_v3 import Prompt
from pprint import pprint
from binance_api import BinanceAPI
from datetime import datetime
prompt = Prompt()
pprint(prompt._calculate_order_pnl())
# import requests
# import json
# def check():
#     frequency = '1m'
#     count = 100
#     symbol = 'BTCUSDT'
#     url = f'https://api.binance.com/api/v3/klines?interval={frequency}&limit={count}&symbol={symbol}' #連結有問題
#     res = requests.get(url)
#     if res.status_code == 400:
#         return False
#     else:
#         return  json.loads(res.text)

# klines = check()
# for kline in klines:
#     # timestamp_s = int(kline[0]) / 1000
#     pprint(kline)
    # print(
    # f'"開盤時間": "{datetime.fromtimestamp(timestamp_s).strftime("%Y-%m-%d %H:%M:%S")}", '
    # f'"開盤價格": {"{:.2f}".format(float(kline[1]))}, '
    # f'"最高價格": {"{:.2f}".format(float(kline[2]))}, '
    # f'"最低價格": {"{:.2f}".format(float(kline[3]))}, '
    # f'"收盤價格": {"{:.2f}".format(float(kline[4]))}, '
    # f'"成交量": {"{:.2f}".format(float(kline[5]))}\n\n, '
    # )
# binance = BinanceAPI(symbol='BTCUSDT')
# kline_info = binance.get_kline_1m()
# for kline in kline_info:
#     timestamp_s = kline[0] / 1000
#     pprint(kline)
#     print(
#     f'"開盤時間": "{datetime.fromtimestamp(timestamp_s).strftime("%Y-%m-%d %H:%M:%S")}", '
#     f'"開盤價格": {"{:.2f}".format(float(kline[1]))}, '
#     f'"最高價格": {"{:.2f}".format(float(kline[2]))}, '
#     f'"最低價格": {"{:.2f}".format(float(kline[3]))}, '
#     f'"收盤價格": {"{:.2f}".format(float(kline[4]))}, '
#     f'"成交量": {"{:.2f}".format(float(kline[5]))}\n\n, '
#     )
# list = [1,2,3,4,5,6,7,8,9,10]
# for i in range(len(list),0, -2):
#     if i == 4:
#         print(list[i-2])
#         del list[i-2]
#         print(list[i-2])

# {'symbol': 'ETHUSDT', 
#  'positionSide': 'BOTH', 
#  'positionAmt': '0.007', 
#  'entryPrice': '3329.93', 
#  'breakEvenPrice': '3331.594965', 
#  'markPrice': '3324.28816667', 
#  'unRealizedProfit': '-0.03949283', 
#  'liquidationPrice': '3175.17703959', 
#  'isolatedMargin': '1.13268285', 
#  'notional': '23.27001716', 
# 'marginAsset': 'USDT', 
# 'isolatedWallet': '1.17217568', 
# 'initialMargin': '1.16350085', 
# 'maintMargin': '0.09308006', 
# 'positionInitialMargin': '1.16350085', 'openOrderInitialMargin': '0', 'adl': 1, 'bidNotional': '0', 'askNotional': '0', 'updateTime': 1738351929004}


