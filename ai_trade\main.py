"""
AI Trade - 主程序入口
增強版 LangChain 交易機器人主入口點
支持 .env 環境變量配置
"""

import os
import sys
from pathlib import Path
from datetime import datetime

# 導入環境變量管理
try:
    from dotenv import load_dotenv
    load_dotenv()  # 載入 .env 文件
    print("✅ .env 文件載入成功")
except ImportError:
    print("⚠️ python-dotenv 未安裝，將使用系統環境變量")
except Exception as e:
    print(f"⚠️ .env 文件載入失敗: {e}")

# 添加當前目錄到 Python 路径
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# 延遲導入，只在需要時導入
EnhancedTradingBot = None


def check_environment():
    """檢查環境變量配置"""
    print("🔍 檢查環境變量配置...")

    required_vars = {
        'GOOGLE_API_KEY': '用於 Gemini AI 模型',
        'binance_api': '幣安 API 密鑰',
        'binance_key': '幣安 API 密鑰'
    }

    missing_vars = []

    for var, description in required_vars.items():
        value = os.getenv(var)
        if value:
            # 只顯示前幾個字符以保護隱私
            masked_value = value[:8] + "..." if len(value) > 8 else "***"
            print(f"✅ {var}: {masked_value} ({description})")
        else:
            print(f"❌ {var}: 未設置 ({description})")
            missing_vars.append(var)

    if missing_vars:
        print(f"\n⚠️ 缺少必要的環境變量: {', '.join(missing_vars)}")
        print("請在 .env 文件中設置或使用系統環境變數")
        return False

    print("✅ 所有必要的環境變量已設置")
    return True


def create_env_template():
    """創建 .env 模板文件"""
    env_template = """# AI Trade 環境變量配置
# 請填入您的實際 API 密鑰

# Google AI API Key (用於 Gemini 模型)
GOOGLE_API_KEY=your_google_api_key_here

# Binance API 配置
binance_api=your_binance_api_key_here
binance_key=your_binance_secret_key_here

# 可選配置
# TRADING_SYMBOL=ETHUSDT
# ANALYSIS_INTERVAL=150
# MODEL_NAME=gemini-2.5-flash-lite-preview-06-17
"""

    env_file = current_dir / '.env'
    if not env_file.exists():
        try:
            with open(env_file, 'w', encoding='utf-8') as f:
                f.write(env_template)
            print(f"📝 已創建 .env 模板文件: {env_file}")
            print("請編輯此文件並填入您的 API 密鑰")
            return True
        except Exception as e:
            print(f"❌ 創建 .env 文件失敗: {e}")
            return False
    else:
        print(f"📄 .env 文件已存在: {env_file}")
        return True


def get_config():
    """獲取配置參數"""
    config = {
        'symbol': os.getenv('TRADING_SYMBOL', 'ETHUSDT'),
        'model_name': os.getenv('MODEL_NAME', 'gemini-2.5-flash-lite-preview-06-17'),
        'interval': int(os.getenv('ANALYSIS_INTERVAL', '150')),
        'max_errors': int(os.getenv('MAX_CONSECUTIVE_ERRORS', '3'))
    }

    print("⚙️ 當前配置:")
    for key, value in config.items():
        print(f"   {key}: {value}")

    return config


def main():
    """主函數"""
    print("🚀 AI Trade - 增強版 LangChain 交易機器人")
    print("=" * 60)
    print(f"啟動時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()

    # 創建 .env 模板（如果不存在）
    create_env_template()

    # 檢查環境變量
    if not check_environment():
        print("\n❌ 環境變量配置不完整，無法啟動")
        print("💡 請按照以下步驟配置:")
        print("   1. 編輯 .env 文件")
        print("   2. 填入您的 API 密鑰")
        print("   3. 重新運行程序")
        return False

    print()

    # 獲取配置
    config = get_config()
    print()

    try:
        # 動態導入交易機器人
        print("🤖 正在導入交易機器人模組...")
        try:
            from enhanced_main import EnhancedTradingBot
        except ImportError as e:
            print(f"❌ 導入 EnhancedTradingBot 失敗: {e}")
            print("請確保安裝了所有必要的依賴庫")
            return False

        # 創建交易機器人
        print("🤖 正在初始化交易機器人...")
        bot = EnhancedTradingBot(
            symbol=config['symbol'],
            model_name=config['model_name']
        )

        # 設置錯誤處理參數
        bot.max_consecutive_errors = config['max_errors']

        print("✅ 交易機器人初始化成功")
        print()

        # 顯示啟動信息
        print("📊 機器人配置:")
        print(f"   交易對: {config['symbol']}")
        print(f"   AI 模型: {config['model_name']}")
        print(f"   分析間隔: {config['interval']} 秒")
        print(f"   最大連續錯誤: {config['max_errors']} 次")
        print()

        print("🎯 開始自動交易...")
        print("按 Ctrl+C 停止程序")
        print("-" * 60)

        # 啟動交易機器人
        bot.start_trading(interval_seconds=config['interval'])

    except KeyboardInterrupt:
        print("\n🛑 收到停止信號")
        print("✅ 程序已安全退出")
        return True

    except Exception as e:
        print(f"\n❌ 程序執行失敗: {str(e)}")
        print("💡 請檢查:")
        print("   1. 網絡連接是否正常")
        print("   2. API 密鑰是否正確")
        print("   3. 依賴庫是否完整安裝")
        return False


def show_help():
    """顯示幫助信息"""
    help_text = """
🤖 AI Trade - 增強版 LangChain 交易機器人

使用方法:
    python main.py              # 啟動交易機器人
    python main.py --help       # 顯示此幫助信息
    python main.py --check      # 僅檢查環境配置

環境變量配置:
    在 .env 文件中設置以下變量:

    GOOGLE_API_KEY              # Google AI API 密鑰 (必需)
    binance_api                 # Binance API 密鑰 (必需)
    binance_key                 # Binance API 密鑰 (必需)

    TRADING_SYMBOL              # 交易對 (可選, 默認: ETHUSDT)
    ANALYSIS_INTERVAL           # 分析間隔秒數 (可選, 默認: 150)
    MODEL_NAME                  # AI 模型名稱 (可選)
    MAX_CONSECUTIVE_ERRORS      # 最大連續錯誤次數 (可選, 默認: 3)

功能特色:
    ✅ 完整的記憶管理系統
    ✅ 高級對話歷史管理
    ✅ 增強版工具系統
    ✅ 專業級提示工程
    ✅ 決策追蹤和學習能力

注意事項:
    ⚠️ 請在測試環境中充分測試後再用於實盤交易
    ⚠️ 建議單筆交易風險不超過總資金的2%
    ⚠️ 定期檢查系統運行狀態和交易結果
"""
    print(help_text)


if __name__ == "__main__":
    # 處理命令行參數
    if len(sys.argv) > 1:
        if sys.argv[1] in ['--help', '-h']:
            show_help()
            sys.exit(0)
        elif sys.argv[1] == '--check':
            print("🔍 環境配置檢查模式")
            create_env_template()
            check_environment()
            sys.exit(0)

    # 運行主程序
    try:
        success = main()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ 程序異常退出: {e}")
        sys.exit(1)