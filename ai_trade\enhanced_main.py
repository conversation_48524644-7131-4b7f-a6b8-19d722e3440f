"""
增強版主程序
使用高級 LangChain 代理進行交易決策
具備完整的記憶管理和決策追蹤功能
"""

from apscheduler.schedulers.blocking import BlockingScheduler
from datetime import datetime, timedelta
import time
import json
import traceback
from typing import Optional, Dict, Any

from advanced_langchain_agent import AdvancedTradingAgent, EnhancedLangchainAgent
from binance_api import BinanceAPI, get_trading_status, get_open_futures_orders


class EnhancedTradingBot:
    """增強版交易機器人"""
    
    def __init__(self, symbol: str = 'ETHUSDT', model_name: str = "gemini-1.5-flash-latest"):
        self.symbol = symbol
        self.model_name = model_name
        
        # 初始化高級代理
        self.agent = AdvancedTradingAgent(
            model_name=model_name,
            symbol=symbol,
            memory_type="buffer_summary"
        )
        
        # 初始化傳統 API（用於狀態檢查）
        self.binance = BinanceAPI(symbol=symbol)
        
        # 狀態追蹤
        self.last_analysis_time = None
        self.consecutive_errors = 0
        self.max_consecutive_errors = 3
        
        print(f"🚀 增強版交易機器人已初始化")
        print(f"📊 交易對: {symbol}")
        print(f"🤖 模型: {model_name}")
        print(f"💾 記憶系統: 已啟用")
        print("-" * 50)
    
    def get_current_trading_status(self) -> Dict[str, Any]:
        """獲取當前交易狀態"""
        try:
            orders = self.binance.get_trading_status()
            main_orders = self.binance.get_open_futures_orders()
            
            status = {
                'has_position': False,
                'order_count': len(orders),
                'main_order_count': len(main_orders),
                'orders': orders,
                'main_orders': main_orders,
                'timestamp': datetime.now().isoformat()
            }
            
            # 判斷是否有持倉
            if len(orders) == 2 and len(main_orders) != 0:
                status['has_position'] = True
                status['main_order'] = main_orders[0]
                
                # 分類止損止盈訂單
                for order in orders:
                    if order.get("type") == "STOP_MARKET":
                        status['stop_loss_order'] = order
                    elif order.get("type") == "TAKE_PROFIT_MARKET":
                        status['take_profit_order'] = order
            
            return status
            
        except Exception as e:
            return {
                'error': f"獲取交易狀態失敗: {str(e)}",
                'timestamp': datetime.now().isoformat()
            }
    
    def execute_trading_cycle(self):
        """執行交易週期"""
        try:
            print(f"\n🔄 開始交易分析週期 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            
            # 獲取當前交易狀態
            trading_status = self.get_current_trading_status()
            
            if 'error' in trading_status:
                print(f"❌ 狀態檢查失敗: {trading_status['error']}")
                self.consecutive_errors += 1
                return
            
            # 根據狀態決定分析類型
            if trading_status['has_position']:
                print("📊 檢測到持倉，執行持倉管理分析...")
                result = self.agent.execute_trading_analysis("position_management")
            else:
                print("🔍 無持倉，執行新交易機會分析...")
                result = self.agent.execute_trading_analysis("new_order")
            
            # 輸出結果
            print(f"🤖 AI 分析結果:")
            print("-" * 40)
            print(result)
            print("-" * 40)
            
            # 重置錯誤計數
            self.consecutive_errors = 0
            self.last_analysis_time = datetime.now()
            
            # 顯示記憶總結
            memory_summary = self.agent.get_memory_summary()
            if memory_summary and memory_summary != "無歷史決策記錄":
                print(f"\n💭 記憶總結:")
                print(memory_summary[:300] + "..." if len(memory_summary) > 300 else memory_summary)
            
        except Exception as e:
            print(f"❌ 交易週期執行失敗: {str(e)}")
            print(f"📋 錯誤詳情: {traceback.format_exc()}")
            self.consecutive_errors += 1
            
            # 如果連續錯誤過多，暫停一段時間
            if self.consecutive_errors >= self.max_consecutive_errors:
                print(f"⚠️ 連續錯誤 {self.consecutive_errors} 次，暫停 5 分鐘...")
                time.sleep(300)  # 暫停 5 分鐘
                self.consecutive_errors = 0
    
    def cleanup_orphaned_orders(self):
        """清理孤立訂單"""
        try:
            trading_status = self.get_current_trading_status()
            
            # 如果有訂單但沒有主訂單，清理這些訂單
            if (trading_status['order_count'] > 0 and 
                trading_status['main_order_count'] == 0):
                
                print("🧹 檢測到孤立訂單，正在清理...")
                for order in trading_status['orders']:
                    try:
                        self.binance.cancel_order(order['orderId'])
                        print(f"✅ 已取消訂單: {order['orderId']}")
                    except Exception as e:
                        print(f"❌ 取消訂單失敗: {e}")
                        
        except Exception as e:
            print(f"❌ 清理孤立訂單失敗: {str(e)}")
    
    def get_status_report(self) -> str:
        """獲取狀態報告"""
        try:
            trading_status = self.get_current_trading_status()
            conversation_history = self.agent.get_conversation_history()
            
            report = f"""
📊 交易機器人狀態報告
時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
交易對: {self.symbol}

💼 持倉狀態: {'有持倉' if trading_status.get('has_position', False) else '無持倉'}
📋 訂單數量: {trading_status.get('order_count', 0)}
🎯 主訂單數量: {trading_status.get('main_order_count', 0)}

🧠 對話歷史: {len(conversation_history)} 條記錄
🔄 上次分析: {self.last_analysis_time.strftime('%H:%M:%S') if self.last_analysis_time else '未執行'}
❌ 連續錯誤: {self.consecutive_errors} 次

💭 記憶狀態: 正常運行
"""
            return report
            
        except Exception as e:
            return f"狀態報告生成失敗: {str(e)}"
    
    def start_trading(self, interval_seconds: int = 150):
        """開始交易"""
        print(f"🎯 交易機器人啟動，分析間隔: {interval_seconds} 秒")
        
        # 清理孤立訂單
        self.cleanup_orphaned_orders()
        
        # 等待到下一個執行點
        wait_time = self.wait_until_next_interval(interval_seconds)
        if wait_time > 0:
            print(f"⏰ 等待 {wait_time:.1f} 秒到下一個執行點...")
            time.sleep(wait_time)
        
        # 設置調度器
        scheduler = BlockingScheduler()
        scheduler.add_job(
            self.execute_trading_cycle,
            'interval',
            seconds=interval_seconds,
            next_run_time=datetime.now()
        )
        
        # 添加狀態報告任務（每小時一次）
        scheduler.add_job(
            lambda: print(self.get_status_report()),
            'interval',
            hours=1,
            next_run_time=datetime.now() + timedelta(minutes=30)
        )
        
        try:
            print("✅ 調度器已啟動，開始自動交易...")
            scheduler.start()
        except KeyboardInterrupt:
            print("\n🛑 收到停止信號，正在關閉...")
            scheduler.shutdown()
            print("✅ 交易機器人已安全關閉")
    
    @staticmethod
    def wait_until_next_interval(interval_seconds: int) -> float:
        """等待到下一個執行間隔"""
        now = datetime.now()
        seconds_since_minute = now.second + now.microsecond / 1000000
        
        # 計算到下一個間隔點的等待時間
        next_interval = ((seconds_since_minute // interval_seconds) + 1) * interval_seconds
        if next_interval >= 60:
            next_interval = 0
            
        wait_time = next_interval - seconds_since_minute
        return max(0, wait_time)


def main():
    """主函數"""
    print("🚀 啟動增強版 LangChain 交易機器人")
    print("=" * 60)
    
    try:
        # 創建交易機器人
        bot = EnhancedTradingBot(
            symbol='ETHUSDT',
            model_name="gemini-1.5-flash-latest"
        )
        
        # 開始交易
        bot.start_trading(interval_seconds=150)
        
    except Exception as e:
        print(f"❌ 啟動失敗: {str(e)}")
        print(f"📋 錯誤詳情: {traceback.format_exc()}")


if __name__ == '__main__':
    main()
