from binance_api import BinanceAPI
from calculate_indicators import TechnicalIndicators  # 導入 TechnicalIndicators 類
from datetime import datetime, timedelta
from pprint import pprint
import json

class Prompt:
# 初始化幣安API
    def __init__(self):
        self.binance = BinanceAPI(symbol='ETHUSDT')
        self.file_path = 'order_record.json'
        self.reason_file = 'reason.txt'
        self.modify_reason_file = 'modify_reason.txt'
        self.no_order_reason_file = 'no_order_reason.txt'

    def format_kline_data(self, kline_data, time_frame):
        """格式化 K 線數據並加入技術指標資訊"""
        if time_frame == '1分鐘':
            indicators = TechnicalIndicators(symbol='ETHUSDT', time_frame='5分鐘') 
            kline_data = kline_data[-30:]
        elif time_frame == '5分鐘':
            indicators = TechnicalIndicators(symbol='ETHUSDT', time_frame='5分鐘') 
            kline_data = kline_data[-120:]
        elif time_frame == '15分鐘':
            indicators = TechnicalIndicators(symbol='ETHUSDT', time_frame='15分鐘') 
            kline_data = kline_data[-120:]
        elif time_frame == '1小時':
            indicators = TechnicalIndicators(symbol='ETHUSDT', time_frame='1小時') 
            kline_data = kline_data[-50:]
        elif time_frame == '4小時':
            indicators = TechnicalIndicators(symbol='ETHUSDT', time_frame='4小時') 
            kline_data = kline_data[-20:]
        elif time_frame == '1天':
            indicators = TechnicalIndicators(symbol='ETHUSDT', time_frame='1天') 
            kline_data = kline_data[-7:]
        
        indicators = indicators.get_kline_and_calculate_indicators()
        formatted_data = f"以下為最近的K線資訊（每根K線代表{time_frame}）：\n"
        for i in range(len(kline_data)-1):
            kline = kline_data[i]
            timestamp_s = kline[0] / 1000
            formatted_data += (
                f'{{"開盤時間": "{datetime.fromtimestamp(timestamp_s).strftime("%Y-%m-%d %H:%M:%S")}", '
                f'"開盤價格": {"{:.2f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.2f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.2f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.2f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.2f}".format(float(kline[5]))}, '
                f'"技術指標資訊": {{'
                f'"平均真實波幅 (ATR)": {indicators["atr"][i]:.2f},'
                f'"移動平均線 (7)": {indicators["moving_average_7"][i]:.2f},'
                f'"移動平均線 (14)": {indicators["moving_average_14"][i]:.2f},'
                f'"指數移動平均線 (EMA)": {indicators["ema"][i]:.2f},'
                f'"相對強弱指數 (RSI)(6)": {indicators["rsi_6"][i]:.2f},'
                f'"相對強弱指數 (RSI)(12)": {indicators["rsi_12"][i]:.2f},'
                f'"相對強弱指數 (RSI)(24)": {indicators["rsi_24"][i]:.2f},'
                f'"MACD": {indicators["macd"][i]:.2f},'
                f'"MACD 信號": {indicators["macd_signal"][i]:.2f},'
                f'"MACD 直方圖": {indicators["macd_hist"][i]:.2f},'
                f'"KDJ K 值": {indicators["kdj_k"][i]:.2f},'
                f'"KDJ D 值": {indicators["kdj_d"][i]:.2f}'
                f'}}}}\n'
            )

        formatted_data += (
            f'\n目前尚未收盤的{time_frame}K線資訊：\n'
            f'{{"開盤時間": "{datetime.fromtimestamp(kline_data[-1][0] / 1000).strftime("%Y-%m-%d %H:%M:%S")}", '
            f'"開盤價格": {"{:.2f}".format(float(kline_data[-1][1]))}, '
            f'"最高價格": {"{:.2f}".format(float(kline_data[-1][2]))}, '
            f'"最低價格": {"{:.2f}".format(float(kline_data[-1][3]))}, '
            f'"最新價格": {"{:.2f}".format(float(kline_data[-1][4]))}, '
            f'"成交量": {"{:.2f}".format(float(kline_data[-1][5]))}}}\n\n'
            )

        # formatted_data += ('\n')
        return formatted_data

    def _calculate_order_pnl(self):
        history_orders = self.binance.get_futures_order_history()
        if len(history_orders) == 0:
            return []
        if history_orders[-1]['realizedPnl'] == '0':
            history_orders.pop()
        open_orders = []
        stop_orders = []
        error_correction = 0
        for i in range(len(history_orders)-1,0,-2):
            i = i + error_correction
            if history_orders[i]['buyer'] != history_orders[i-1]['buyer'] and float(history_orders[i]['qty']) == float(history_orders[i-1]['qty']):
                open_orders.append(history_orders[i-1])
                stop_orders.append(history_orders[i])
            elif float(history_orders[i]['qty']) > float(history_orders[i-1]['qty']):
                buy_order = {
                    "time": history_orders[i-1]['time'],
                    "price": history_orders[i-1]['price'],
                    "quoteQty": float(history_orders[i-1]['quoteQty']) + float(history_orders[i-2]['quoteQty']),
                    "side": history_orders[i]['side'],
                    "realizedPnl": float(history_orders[i]['realizedPnl']) + float(history_orders[i-2]['realizedPnl']),
                    "commission": float(history_orders[i]['commission']) + float(history_orders[i-2]['commission'])
                }
                open_orders.append(buy_order)
                stop_orders.append(history_orders[i])
                history_orders.remove(history_orders[i-2])
                error_correction -= 1
            elif float(history_orders[i]['qty']) < float(history_orders[i-1]['qty']):
                stop_order = {
                    "time": history_orders[i]['time'],
                    "price": history_orders[i]['price'],
                    "quoteQty": float(history_orders[i]['quoteQty']) + float(history_orders[i-2]['quoteQty']),
                    "side": history_orders[i]['side'],
                    "realizedPnl": float(history_orders[i]['realizedPnl']) + float(history_orders[i-1]['realizedPnl']),
                    "commission": float(history_orders[i]['commission']) + float(history_orders[i-1]['commission'])
                }
                open_orders.append(history_orders[i-2])
                stop_orders.append(stop_order)
                history_orders.remove(history_orders[i-1])
                error_correction -= 1

        results = []  # 儲存計算結果的列表
        for i in range(min(len(open_orders), len(stop_orders)),0,-1): # 修正索引超出範圍的錯誤
            open_order = open_orders[i-1]
            stop_order = stop_orders[i-1]
            stop_price = float(stop_order['price'])
            entry_price = float(open_order['price'])
            pnl = float(stop_order['realizedPnl'])
            open_order_commission = float(open_order['commission'])
            stop_order_commission = float(stop_order['commission'])
            results.append({
                "update_time": datetime.fromtimestamp(open_order['time'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                "stop_time": datetime.fromtimestamp(stop_order['time'] / 1000).strftime('%Y-%m-%d %H:%M:%S'),
                "side": open_order['side'],
                "open_price": round(entry_price,2),
                "stop_price": round(stop_price,2),
                "pnl_percentage": f"{(pnl/(float(open_order['quoteQty']) / 20))*100:.2f}",# 計算盈虧百分比
                "net_pnl_percentage": f"{((pnl-open_order_commission-stop_order_commission)/(float(open_order['quoteQty']) / 20))*100:.2f}"
            })

        return results  # 返回結果列表
    
    def get_prompt_for_new_order(self):
        """生成新的訂單提示詞"""
        # 獲取技術指標
        with open(self.reason_file, 'r', encoding='utf-8') as f:
            reason = f.read()
        with open(self.modify_reason_file, 'r', encoding='utf-8') as f:
            modify_reason = f.read()
        with open(self.no_order_reason_file, 'r', encoding='utf-8') as f:
            no_order_reason = f.read()
        # kline_info_1m = self.binance.get_kline_1m()
        kline_info_5m = self.binance.get_kline_5m()
        kline_info_15m = self.binance.get_kline_15m()
        kline_info_1h = self.binance.get_kline_1h()
        kline_info_4h = self.binance.get_kline_4h()
        # kline_info_1d = self.binance.get_kline_1d()
        price_info = self.binance.get_price()
        order_history = self._calculate_order_pnl()
        prompt = (
            "你是一名專注於短期波段交易的交易員，主要依據短周期K線（如5分鐘、15分鐘）捕捉入場信號，\n"
            "而將1小時及4小時K線僅作為輔助，決定持倉時間和風險管理。你的目標是在短時間週期迅速捕捉盈利機會，\n"
            "並在市場短期波動中及時出場，避免頻繁交易導致手續費過高及風險擴大。\n\n"
            "【注意事項】：\n"
            "1. 訂單將以20倍槓桿進行，請在價格最優時進場，以避免高槓桿引發過多手續費和風險擴大。\n"
            "2. 入場點位主要依據短週期K線（如5分鐘、15分鐘）的形態和成交量變化，\n"
            "   並輔以RSI、MACD、ATR、吞沒形態及十字星等技術指標進行綜合判斷；\n"
            "3. 大周期K線（1小時、4小時）僅用來確認大趨勢和市場整體方向，但不限制短期交易的入場決策。\n"
            "4. 不支持補倉或部分止盈止損操作，請在開倉時確保開倉點位風險可控。\n"
            "5. 若是當前處於盤整階段，只需簡單的在壓力位做空，支撐位做多即可。\n\n"
            "【入場策略】：\n"
            "  - 可以考慮在支撐位做多，壓力位做空。\n"
            "  - 若是空頭佔優時發現短期內技術指標或是K線型態有反彈跡象，請**耐心觀望直至反彈結束再做空**；反之多頭佔優但短期內技術指標或是K線型態有回落跡象，請**耐心觀望直至回落到位再做多**。\n"
            "  - 如果近期短周期K線出現長上影線（影線長度約為實體的兩倍），且價格回調至前一根K線低點，則可能表明賣盤在短期內積極反應，考慮做空。\n"
            "  - 如果近期短周期K線出現長下影線（影線長度約為實體的兩倍），且價格回升至前一根K線高點，則可能表明買盤在短期內積極介入，考慮做多。\n"
            "  - 當市場出現短期縮量回調或反彈時，依據短周期K線信號進行交易，並根據大周期數據靈活調整持倉時間。\n"
            "  - 當短期均線（如5分鐘、15分鐘）發生金叉或死叉時，也可作為入場參考信號。\n"
            "  - 如果市場出現強烈的動量信號（例如RSI迅速突破超買/超賣區域或MACD形成明顯交叉），則可做出相應的操作（例如：MACD金叉形成時做多；死叉形成時做空）。\n"
            "  - 除非出現非常明顯的市場反轉信號（例如大量交易量伴隨極長上下影線、吞沒形態或十字星出現），否則必須依據短期K線信號進場。\n\n"
            "請根據上述原則，綜合分析當前短期市場趨勢、各時間周期K線數據及技術指標，在最佳點位開單，如果錯過最佳點位則保持觀望，以免造成損失。\n"
        )
        if float(order_history[-1]['net_pnl_percentage']) > 0:
            profit = "win"
        else:
            profit = "loss"
        if len(order_history) != 0:
            prompt += (
            "以下是你最近執行的歷史訂單資訊（最近已成交訂單）：\n"
            )
            prompt += "以下是最近的歷史訂單記錄：\n"
            for order in order_history[-5:]:
                prompt += (
                    f"  - 訂單時間：{order['update_time']}\n"
                    f"  - 平倉時間：{order['stop_time']}\n"
                    f"  - 訂單方向：{'做多' if order['side'] == 'BUY' else '做空'}\n"
                    f"  - 開倉價格：{order['open_price']}\n"
                    f"  - 平倉價格：{order['stop_price']}\n"
                    f"  - 盈虧百分比：{order['pnl_percentage']}%\n"
                    f"  - 淨盈虧百分比：{order['net_pnl_percentage']}%\n\n"
                )
            if reason:
                prompt += f"最近一次訂單開單原因：\n- {reason}\n"
            if modify_reason:
                prompt += f"最近一次訂單{"止盈" if profit == 'win' else "止損"}原因：\n- {modify_reason}\n\n"
            time_obj = datetime.strptime(order_history[-1]['update_time'] , "%Y-%m-%d %H:%M:%S")
            now = datetime.now()
            time_diff = now - time_obj
            if abs(time_diff) < timedelta(minutes=15):
                if profit == 'win':
                    prompt += (
                            "提示：最近一次訂單紀錄止盈，分析上次止盈原因結合當前市場資訊，若是趨勢趨緩或是短期內有反彈風險請暫時觀望，**避免過度交易導致盈利轉為虧損以及付出高額手續費**。\n\n"
                            )
                else:
                    prompt += (
                            "提示：最近一次訂單紀錄止損，請判斷止損原因是否有反轉，如果沒有明顯反轉請勿再次交易，避免虧損的同時還付出高額手續費。\n\n"
                            )
            else:
                prompt += (
                    "提示：請根據最近一次訂單的盈虧情況，判斷是否有新的交易機會，並提出新的交易建議。\n\n"
                )
        if no_order_reason:
            prompt += f"最近一次選擇觀望的原因：\n- {no_order_reason}\n\n"
            prompt += ("提示：根據最近一次選擇觀望的原因，判斷：\n"
                       "1. 是否有指標推翻了上次觀望的原因？\n"
                       "2. 是否有新的交易機會？\n"
                       "3. 是否有新的支撐/壓力位來來確認趨勢轉變？\n"
                       "4. 如果決定繼續保持觀望，提出當前不適合做多或做空的原因？\n\n")    

        prompt +=(    
            "以下是當前市場資訊：\n"    
            f"- 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"- 當前價格: {price_info['symbol']} = {"{:.1f}".format(float(price_info['price']))}\n\n"
        )
        print(f"- 當前價格: {price_info['symbol']} = {"{:.1f}".format(float(price_info['price']))}")
        # prompt += self.format_kline_data(kline_info_1m, "1分鐘")
        prompt += self.format_kline_data(kline_info_5m, "5分鐘")
        prompt += self.format_kline_data(kline_info_15m, "15分鐘")
        prompt += self.format_kline_data(kline_info_1h, "1小時")
        prompt += self.format_kline_data(kline_info_4h, "4小時")
        #prompt += self.format_kline_data(kline_info_1d, "1天")
        # 在生成新訂單提示詞時加入以下邏輯
        if len(order_history) > 0 and profit == 'loss':
            prompt += (
                "### 前次止損後開單額外條件：\n"
                f"因最近一次訂單止損虧損，需滿足以下 **全部條件** 才允許做{"多" if order_history[-1]['side'] == 'BUY' else "空"}：\n"
                "1. 價格已反向突破前次訂單開倉價的 ±1% 範圍\n"
                "2. 5分鐘級別K線出現至少兩根確認趨勢反轉的K線（如吞沒形態）\n"
                f"3. RSI(24) 較前次開單時偏離超過5點（{"例如原做多時RSI從40升至45" if order_history[-1]['side'] == 'BUY' else "例如原做空時RSI從50降至45"}）\n"
                "4. MACD直方圖連續3根K線擴大反向動能\n\n"
                "即使達成條件，也應當根據入場策略以及近期K線資訊技術指標等綜合判斷當前是否是進場的最佳點位！\n\n"
            )
        elif len(order_history) > 0 and profit == 'win':
            prompt += (
                "### 前次止盈後開單額外條件：\n"
                f"因最近一次訂單止盈，需滿足以下 **全部條件** 才允許做{"多" if order_history[-1]['side'] == 'BUY' else "空"}：\n"
                "1. 價格已突破前次止盈價的 ±1.5% 範圍，且回測後站穩至少2根5分鐘K線\n"
                "2. 5分鐘級別K線出現至少3根同方向強實體K線（實體長度 > 平均波動的1.5倍）\n"
                f"3. RSI(6) 較前次止盈時維持相同方向偏離（{"例如原做多時RSI仍高於55並上升" if order_history[-1]['side'] == 'BUY' else "例如原做空時RSI仍低於45並下降"}）\n"
                "4. MACD直方圖連續3根K線擴大同方向動能\n"
                "5. 交易量較前次止盈時增加15%以上\n\n"
                "若未達標，應自上次開單時間後等待至少30分鐘再評估；即使達成條件，也應當根據入場策略以及近期K線資訊技術指標等綜合判斷當前是否是進場的最佳點位！\n\n"
            )
        prompt += (
            "請根據以上市場資訊進行分析，並回答以下問題：\n"
            "1. 當前是否存在合適的交易機會？\n"
            "2. 如果存在交易機會，請提供建議的買入或賣出方向、價格範圍，以及對應的止損和止盈價格。\n"
            "3. 請簡要描述你的交易建議背後的原因、邏輯。\n\n"

            "請嚴格遵守以下格式回覆：\n"
            '{"交易機會": "是/否", "action": "BUY/SELL/null", "建議價格": 價格, "交易信心指數": 0-100,\n'
            '"止損價格": 價格, "止盈價格": 價格, "交易理由": "原因描述，並且給後續的止盈止損操作建議！"}\n'
        )
        return prompt
    

    def get_prompt_for_stop_loss_or_take_profit(self, order_record):
        with open(self.reason_file, 'r') as f:
            reason = f.read()
        with open(self.modify_reason_file, 'r') as f:
            modify_reason = f.read()
        with open(self.file_path, 'r') as f:
            order_open_info = json.load(f)
        
        target_pnl = 0.20*100
        """生成止盈或止損的提示詞，目標收益率默認為20%"""
        
        if order_record is None:
            return "無法加載訂單記錄。"

        print("查詢歷史K線資訊...")
        # kline_info_1m = self.binance.get_kline_1m()
        kline_info_5m = self.binance.get_kline_5m()
        kline_info_15m = self.binance.get_kline_15m()
        kline_info_1h = self.binance.get_kline_1h()
        kline_info_4h = self.binance.get_kline_4h()
        #kline_info_1d = self.binance.get_kline_1d()
        order_history = self._calculate_order_pnl()
        try:
            main_order = self.binance.get_open_futures_orders()[0]
        except Exception as e:
            with open('error.txt','a',encoding='utf-8') as file:
                file.write(f"{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}\n") 
                file.write(f"API Exception: {e}\n")
            print("無法獲取當前訂單信息！")
            return
        stop_loss_order = order_record.get('stop_loss_order')
        take_profit_order = order_record.get('take_profit_order')
        mark_price = float(main_order['markPrice'])
        break_even_price = float(main_order['breakEvenPrice'])  # 打和價格
        unrealized_profit = float(main_order['unRealizedProfit'])  # 未實現盈虧
        notional = abs(float(main_order['notional']))  # 持倉名義金額
        leverage = 20  # 杠杆倍數
        pnl_rate = unrealized_profit *100 / (notional / leverage)
        position_amt = abs(float(main_order['positionAmt']))
        if take_profit_order['stopPrice'] > stop_loss_order['stopPrice']:
            side = 'BUY'
        else:
            side = 'SELL'
        prompt = (
            "你是一名專注於短期波段交易的交易員，擅長運用K線數據和技術指標做出精確的止盈止損決策。\n"
            "當前，你需要根據最新市場情況，對已開啟的訂單進行動態止盈止損調整，以保護盈利或控制虧損。\n"
            "你將每150秒獲取一次市場資訊，並根據短周期K線（5分鐘、15分鐘）的變化做出快速反應；\n"
            "而1小時及4小時K線僅作為輔助，主要用來調整持倉時間，不作為動態止盈止損決策的主要依據。\n"
            "注意：此訂單已經開啟，若非明顯的反轉趨勢請**盡可能持有訂單**直到止盈價格，以免短時間內因為重複同樣的交易付出高額手續費！\n"
            "【重要事項】：\n"
            "1. 當前使用20倍槓桿，市場波動風險較大，請嚴格按照短期K線信號調整止盈止損，保護已有盈利或及時止損。\n"
            "2. 重點關注短周期K線中的價格波動及成交量變化；例如：\n"
            "   - 如果做多訂單中，短期K線出現大量交易量伴隨極長上影線（插針型反轉信號），表示市場可能迅速反轉下跌，應提前提高止損或考慮平倉；\n"
            "   - 如果做空訂單中，短期K線出現大量交易量伴隨極長下影線，則可能預示市場反轉上漲，應提前提高止損或平倉保護盈利。\n"
            "3. 其他短期策略參考：\n"
            "   - 當短期均線（5分鐘均線、15分鐘）發生金叉或死叉，或RSI、MACD等動量指標出現快速變化時，應作為調整止盈止損的參考依據；\n"
            "   - 如果市場價格突破短期阻力位或支撐位，且隨後出現回測確認的情況，也應及時調整止盈止損以鎖定盈利。\n"
            "4. 長周期K線（1小時、4小時）僅用作輔助，主要用於確定整體市場趨勢及持倉時間；即使短期信號與大周期趨勢略有背離，也不影響根據短周期數據調整止盈止損，但必須注意持倉可能較短。\n"
            "5. 如果市場在短期內出現明顯反轉信號（如大量交易量伴隨極長上下影線、吞沒形態或十字星出現），而訂單仍處於盈利狀態，請優先保護已有盈利，切勿因過度等待而使盈利縮水或轉虧。\n"
            "6. 若是當前處於盤整階段，只需簡單地將做多訂單在壓力位止盈或是將做空訂單在支撐位止盈即可。\n"
            "7. 訂單持有時間也是重要考量因素，在訂單持有時間較短（15分鐘內）的情況下，如果市場沒有出現明顯反轉趨勢，請放寬止損多給訂單機會；反之訂單持有時間較長，則應當考慮止盈止損。\n\n"
            "【動態止盈止損操作基本策略】：\n"
            "  - 做多時：\n"
            "      若價格短期回調並出現明顯的反轉信號（例如上影線特別長且伴隨放量），則應提前提高止損點位，或直接以市價止盈平倉，以避免利潤回吐。\n"
            "  - 做空時：\n"
            "      若價格短期反彈並出現明顯的反轉信號（例如下影線特別長且伴隨放量），則應提前提高止損點位（降低止損價格），或直接以市價止盈平倉。\n"
            "請根據上述原則，綜合分析當前市場短期K線數據、量價關係及技術指標，\n"
            "給出具體的動態止盈止損操作建議，以保護已有盈利或控制虧損。\n\n"
        )
        
        if reason:
            prompt += (
                "### 開倉時的核心邏輯（必須驗證是否仍成立）：\n"
                f"- {reason}\n\n"
                "請嚴格檢查以下條件：\n"
                "1. 當前市場是否仍符合上述開倉邏輯？\n"
                "2. 是否有新的技術指標或事件推翻原邏輯？\n"
                "3. 若原邏輯未失效，請避免因短期波動調整止損。\n\n"
            )
            confidence = int(order_open_info.get("交易信心指數"))
            prompt += (
                f"### 開倉信心指數（{confidence}/100）：\n"
                "信心指數越高，代表開倉邏輯越強，應給予更多波動容忍度。\n"
                f"- 若信心指數 ≥80：需強烈反轉信號才建議止損。\n"
                f"- 若信心指數 ≤50：可根據短期指標靈活調整。\n\n"
            )
        if modify_reason:
            prompt += f"上次修改訂單的原因是：\n- {modify_reason}\n\n"

        prompt +=(
                "提示：\n"
                "1. 根據以上原因，分析並決定本次操作的最佳策略。\n"
                "2. 請嚴格遵守開倉思路以及止盈止損建議並結合最新市場情況進行止盈止損操作。\n"
                "3. 若市場沒有出現極為明顯的反轉信號證實開倉思路錯誤，請勿輕易止損。\n\n"
                )
        
        if len(order_history) != 0:
            prompt += "以下是最近的歷史訂單記錄：\n"
            for order in order_history[-5:]:
                prompt += (
                    f"  - 訂單時間：{order['update_time']}\n"
                    f"  - 平倉時間：{order['stop_time']}\n"
                    f"  - 訂單方向：{'做多' if order['side'] == 'BUY' else '做空'}\n"
                    f"  - 開倉價格：{order['open_price']}\n"
                    f"  - 平倉價格：{order['stop_price']}\n"
                    f"  - 盈虧百分比：{order['pnl_percentage']}%\n"
                    f"  - 淨盈虧百分比：{order['net_pnl_percentage']}%\n\n"
                )
        
            prompt += "提示：根據歷史操作優化本次止盈止損策略。\n\n"
        
        if side == 'BUY':
            net_profit = ((mark_price - break_even_price)*position_amt)*100/(notional / leverage)
        elif side == 'SELL':
            if mark_price < break_even_price:
                net_profit =  ((break_even_price - mark_price)*position_amt)*100/(notional / leverage)
            else:
                net_profit = -((mark_price - break_even_price)*position_amt)*100/(notional / leverage)
        # 修改原有 pnl_rate 條件
        if abs(pnl_rate) < 1.0:  # 當盈虧在±1%範圍內時
            prompt += (
                "### 微幅波動策略：\n"
                "當前盈虧波動小於1%，需同時滿足以下條件才操作：\n"
                "1. 價格在成本價附近盤整超過30分鐘\n"
                "2. 波動率（ATR）下降至開倉時的50%以下\n"
                "否則應保持原有止損設定，等待趨勢發展。\n\n"
            )
        elif pnl_rate >= 5 and net_profit <= 15:  # 原為 10%
            prompt += (
                "### 盈利策略調整（放寬止盈條件）：\n"
                "浮盈 5%~15% 時，需出現至少兩根短周期 K 線的反轉信號才建議止盈。\n"
            )
        elif pnl_rate > 15:
            prompt += (
                "### 盈利時的短期止盈止損策略：\n"
                "1. 當盈利超過15%時，市場已進入較明顯盈利階段，此時應迅速鎖定利潤。\n"
                "   - 請根據短周期 K 線信號，迅速將止損價格向有利方向調整，確保至少鎖定70%以上的現有盈利。\n"
                "   - 如果短周期 K 線出現明顯反轉信號或是趨勢趨緩（例如大量交易量伴隨極長影線、吞沒形態或十字星），則應立即以市價平倉。\n"
                "2. 目標在於及時保護盈利，防止市場短期反轉導致收益縮水。\n"
                "### 目標：\n"
                "快速鎖定盈利，避免因市場反轉而使利潤回吐。\n\n"
            )
        elif pnl_rate < -4:
            prompt += (
                "### 虧損時的短期止盈止損策略：\n"
                "1. 當訂單處於虧損狀態時，應嚴格控制虧損，立即止損以防進一步擴大虧損。\n"
                "2. 觀察短周期 K 線及成交量，若有明顯反轉跡象，應果斷止損平倉。\n"
                "3. 若是市場趨勢依然符合開單時的思路，則禁止改變止損價格；但若市場趨勢明顯反轉，則應立即止損平倉。\n"
                "### 目標：\n"
                "分析最新市場趨勢，控制虧損，保護本金。\n\n"
            )
        else:
            prompt += (
                "### 當前止盈止損策略：\n"
                "1. 目前盈利不足，應保留更多波動空間，避免因短期波動而過早止損。\n"
                "2. 密切關注短周期 K 線的變化，若出現初步反轉信號（例如成交量激增伴隨極長影線或均線交叉），應及時調整止盈止損，保護現有盈利或避免虧損擴大。\n"
                "3. 如果市場趨勢明顯反轉，則應根據短周期信號及時平倉出場；否則請盡可能持有訂單。\n"
                "4. 若是市場趨勢依然符合開單時的思路，則禁止改變止損價格，以免過早止損。\n"
                "### 目標：\n"
                "盡可能**持有訂單**，以達到收益最大化，除非持有訂單時間過長，市場趨勢可能反轉，否則不輕易以市價止損。\n\n"
            )

        time_diff = datetime.now() - datetime.fromtimestamp(main_order['updateTime'] / 1000)
        if time_diff < timedelta(minutes=30) and pnl_rate < 10:
            prompt += (
                "### 持倉時間提示：\n"
                "當前訂單持有時間短於 30 分鐘，若無強烈反轉信號，請勿輕易止損。\n"
                "短期波動屬正常現象，需至少觀察 3 根 5 分鐘 K 線再決策。\n\n"
            )
        prompt += (
            "### 可視為反轉信號的條件（需同時滿足兩項以上）：\n"
            "1. 短周期K線出現吞沒形態且成交量 > 前5根均值 * 1.5。\n"
            "2. RSI(6) 在單根K線內波動超過15點。\n"
            "3. MACD 直方圖連續 3 根 K 線反向擴大（數值差 > 前值50%）。\n"
            "4. 價格突破開倉邏輯中定義的關鍵位（例如原支撐位轉壓力位）\n"
            "止盈止損前請嚴格檢查以上條件，避免因短期波動而過早止損。\n\n"
        )
        # 判斷當前止損是否設在成本價附近（浮動0.1%範圍內）
        is_breakeven_stop = abs(float(stop_loss_order['stopPrice']) - float(main_order['breakEvenPrice'])) / float(main_order['breakEvenPrice']) <= 0.001

        if is_breakeven_stop:
            direction_desc = "低於" if side == 'BUY' else "高於"
            rsi_action = "跌破40並持續下行" if side == 'BUY' else "升破60並持續上行"
            prompt += (
                "### 當前止損位於成本價的特殊策略：\n"
                "1. 需滿足以下 **至少兩項** 條件：\n"
                f"   - 5分鐘K線連續3根收盤價{direction_desc}成本價\n"
                f"   - RSI(6) {rsi_action}\n"
                "   - 成交量較開倉時下降50%且持倉量減少10%\n"
                "2. 若持倉時間 <30分鐘，需額外滿足：4小時級別趨勢反向突破\n\n"
            )
        prompt += (
            f"- 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"- 當前價格: {main_order['symbol']} = {"{:.1f}".format(float(main_order['markPrice']))}\n\n"
        )
        
        # prompt += self.format_kline_data(kline_info_1m, "1分鐘")
        prompt += self.format_kline_data(kline_info_5m, "5分鐘")
        prompt += self.format_kline_data(kline_info_15m, "15分鐘")
        prompt += self.format_kline_data(kline_info_1h, "1小時")
        prompt += self.format_kline_data(kline_info_4h, "4小時")
        #prompt += self.format_kline_data(kline_info_1d, "1天")

        # 是否達到目標收益率
        is_target_reached = pnl_rate >= target_pnl
        if pnl_rate >= 10:
            prompt += f"\n提示：**浮盈為 {"{:.2f}".format(net_profit)}%，應逐步提高止損，確保至保持 {"{:.2f}".format(net_profit*0.7)} 以上的利潤。**\n"
        elif pnl_rate >= 5:
            prompt += "\n提示：**當前盈利超過 5%，請立即調整止損，確保至少 3-4% 的盈利！**\n"

        prompt += (
            "以下為當前訂單資訊：\n"
            f"  - 開單時間：{datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 訂單方向：{'做多' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else '做空'}\n"
            f"  - 主訂單價格：{main_order['entryPrice']}\n"
            f"  - 止損價格：{stop_loss_order.get('stopPrice')}\n"
            f"  - 止盈價格：{take_profit_order.get('stopPrice')}\n"
            f"  - 強制平倉價格：{"{:.2f}".format(float(main_order['liquidationPrice']))}\n"
            f"  - 成本價格(即加入手續費等成本後的價格)：{"{:.2f}".format(float(main_order['breakEvenPrice']))}\n"
            f"  - 淨利潤：{"{:.2f}".format(net_profit)}%\n"
            f"  - 當前收益率：{"{:.2f}".format(pnl_rate)}%\n"
        )
        prompt += (
            "訂單資訊名稱提示：\n"
            "1. 成本價格 (可作為止損參考價格)：表示在計算盈虧時，已考慮手續費等交易成本後的實際成本價格，使盈虧為0的價格。\n"
            "2. 淨利潤：根據該訂單計算出的實際盈利百分比，扣除了相關成本後的淨盈利情況。\n"
        )
        print(
            "以下為當前訂單資訊：\n"
            f"  - 開單時間：{datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 訂單方向：{'做多' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else '做空'}\n"
            f"  - 主訂單價格：{main_order['entryPrice']}\n"
            f"  - 止損價格：{stop_loss_order.get('stopPrice')}\n"
            f"  - 止盈價格：{take_profit_order.get('stopPrice')}\n"
            f"  - 強制平倉價格：{"{:.2f}".format(float(main_order['liquidationPrice']))}\n"
            f"  - 成本價格(即加入手續費等成本後的價格)：{"{:.2f}".format(float(main_order['breakEvenPrice']))}\n"
            f"  - 淨利潤：{"{:.2f}".format(net_profit)}%\n"
            f"  - 當前收益率：{"{:.2f}".format(pnl_rate)}%\n"
        )
        print(f"  - 當前價格：{"{:.2f}".format(float(main_order['markPrice']))}")
        if pnl_rate > 15:
            prompt += (            
            "\n**重要提醒**：\n"
            "如果你認為盈利已經達到目標，請選擇市價止盈，而不是調整止盈止損。\n"
            "市場瞬息萬變，過度等待可能會導致盈利縮水。\n"
            "若認為市場趨勢即將反轉，應**立即止盈，而非等待更高點。**\n"
            )
        prompt += (
            "請嚴格按照以下格式回覆：\n"
            '{"action": "STOP_LOSS_TAKE_PROFIT", "理由": "選擇立即止盈止損的理由"}\n'
            "\n若你認為需要修改止盈止損設定，請嚴格按照以下格式回覆：\n"
            '{"action": "MODIFY_STOP_LOSS_TAKE_PROFIT", "止損價格": 價格, "止盈價格": 價格, "理由": "分析市場情況並給出修改理由"}\n'
            "\n若認為止盈止損設定合理，請嚴格按照以下格式回覆：\n"
            '{"action": null, "理由": "簡要分析"}\n'
        )

        return prompt


