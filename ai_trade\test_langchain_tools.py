#!/usr/bin/env python3
"""
測試 LangChain 工具調用架構
"""

import os
import sys
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_tool_imports():
    """測試工具導入"""
    print("=== 測試工具導入 ===")
    
    try:
        # 測試 Binance 工具導入
        from binance_api import (
            execute_buy_order, execute_sell_order, 
            modify_stop_loss_take_profit, close_position_immediately,
            get_price, set_default_symbol
        )
        print("✅ Binance 交易工具導入成功")
        
        # 測試技術指標工具導入
        from calculate_indicators import get_kline_and_calculate_indicators
        print("✅ 技術指標工具導入成功")
        
        # 測試 LangChain Agent 導入
        from langchain_agent import (
            LangchainAgent, get_market_data, 
            get_technical_indicators_for_timeframe
        )
        print("✅ LangChain Agent 工具導入成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 工具導入失敗: {e}")
        return False

def test_tool_structure():
    """測試工具結構"""
    print("\n=== 測試工具結構 ===")
    
    try:
        from binance_api import execute_buy_order, execute_sell_order
        from langchain_agent import LangchainAgent
        
        # 測試工具是否有正確的屬性
        print(f"1. execute_buy_order 工具名稱: {execute_buy_order.name}")
        print(f"2. execute_sell_order 工具名稱: {execute_sell_order.name}")
        
        # 測試 Agent 工具列表
        agent = LangchainAgent(symbol='ETHUSDT')
        tools = agent.get_available_tools()
        print(f"3. Agent 可用工具數量: {len(tools)}")
        print(f"4. 工具名稱列表: {[tool.name for tool in tools]}")
        
        # 驗證必要的交易工具都存在
        tool_names = [tool.name for tool in tools]
        required_tools = [
            'execute_buy_order', 'execute_sell_order', 
            'modify_stop_loss_take_profit', 'close_position_immediately'
        ]
        
        missing_tools = [tool for tool in required_tools if tool not in tool_names]
        if missing_tools:
            print(f"❌ 缺少必要工具: {missing_tools}")
            return False
        
        print("✅ 所有必要的交易工具都已正確配置")
        return True
        
    except Exception as e:
        print(f"❌ 工具結構測試失敗: {e}")
        return False

def test_agent_creation():
    """測試 Agent 創建"""
    print("\n=== 測試 Agent 創建 ===")
    
    try:
        from langchain_agent import LangchainAgent
        
        # 創建 Agent
        agent = LangchainAgent(symbol='ETHUSDT')
        print("✅ LangchainAgent 創建成功")
        
        # 測試 Agent 屬性
        print(f"   - 交易對: {agent.symbol}")
        print(f"   - 工具數量: {len(agent.tools)}")
        print(f"   - 是否有 agent_executor: {hasattr(agent, 'agent_executor')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Agent 創建失敗: {e}")
        return False

def test_market_context_generation():
    """測試市場背景信息生成"""
    print("\n=== 測試市場背景信息生成 ===")
    
    try:
        from langchain_agent import LangchainAgent
        
        agent = LangchainAgent(symbol='ETHUSDT')
        
        # 測試新訂單背景信息
        print("1. 測試新訂單背景信息生成...")
        new_order_context = agent.get_market_context_for_new_order()
        print(f"   背景信息長度: {len(new_order_context)} 字符")
        print(f"   包含當前時間: {'當前時間' in new_order_context}")
        
        # 測試持倉管理背景信息
        print("2. 測試持倉管理背景信息生成...")
        position_context = agent.get_market_context_for_position_management(None)
        print(f"   背景信息長度: {len(position_context)} 字符")
        
        print("✅ 市場背景信息生成測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 市場背景信息生成測試失敗: {e}")
        return False

def test_main_integration():
    """測試主程序集成"""
    print("\n=== 測試主程序集成 ===")
    
    try:
        from main import ask_langchain_agent, load_order_record
        
        print("1. 測試主程序函數導入...")
        print(f"   ask_langchain_agent: {ask_langchain_agent}")
        print(f"   load_order_record: {load_order_record}")
        
        print("✅ 主程序集成測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 主程序集成測試失敗: {e}")
        return False

def test_tool_documentation():
    """測試工具文檔"""
    print("\n=== 測試工具文檔 ===")
    
    try:
        from binance_api import execute_buy_order, execute_sell_order
        
        # 檢查工具是否有正確的文檔字符串
        print("1. 檢查 execute_buy_order 文檔...")
        if execute_buy_order.__doc__:
            print(f"   ✅ 有文檔字符串 ({len(execute_buy_order.__doc__)} 字符)")
        else:
            print("   ❌ 缺少文檔字符串")
            
        print("2. 檢查 execute_sell_order 文檔...")
        if execute_sell_order.__doc__:
            print(f"   ✅ 有文檔字符串 ({len(execute_sell_order.__doc__)} 字符)")
        else:
            print("   ❌ 缺少文檔字符串")
        
        print("✅ 工具文檔測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 工具文檔測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print(f"開始測試 LangChain 工具調用架構 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    tests = [
        test_tool_imports,
        test_tool_structure,
        test_agent_creation,
        test_market_context_generation,
        test_main_integration,
        test_tool_documentation
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 70)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！LangChain 工具調用架構重構成功！")
        print("\n重構總結:")
        print("✅ 使用 @tool 裝飾器包裝所有交易功能")
        print("✅ LLM 可以直接調用交易工具執行操作")
        print("✅ 移除了 JSON 格式返回，改為直接工具調用")
        print("✅ 保持了原有的交易功能和邏輯")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查相關組件")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
