from binance_api import BinanceAPI
from datetime import datetime

class Prompt:
# 初始化幣安API
    def __init__(self):
        self.binance = BinanceAPI(symbol='ETHUSDT')
        self.file_path = 'order_record.json'

    def format_kline_data(self, kline_data, time_frame):
        """格式化 K 線數據"""
        if time_frame == '15分鐘':
            kline_data = kline_data[-80:]
        elif time_frame == '1小時':
            kline_data = kline_data[-20:]
        formatted_data = f"以下為最近的K線資訊（每根K線代表{time_frame}）：\n"
        for kline in kline_data[:-1]:
            timestamp_s = kline[0] / 1000
            formatted_data += (
                f'{{"開盤時間": "{datetime.fromtimestamp(timestamp_s).strftime("%Y-%m-%d %H:%M:%S")}", '
                f'"開盤價格": {"{:.1f}".format(float(kline[1]))}, '
                f'"最高價格": {"{:.1f}".format(float(kline[2]))}, '
                f'"最低價格": {"{:.1f}".format(float(kline[3]))}, '
                f'"收盤價格": {"{:.1f}".format(float(kline[4]))}, '
                f'"成交量": {"{:.1f}".format(float(kline[5]))}}}\n'
            )
        formatted_data += (
            f'\n最新的{time_frame}K線資訊：\n'
            f'{{"開盤時間": "{datetime.fromtimestamp(kline_data[-1][0] / 1000).strftime("%Y-%m-%d %H:%M:%S")}", '
            f'"開盤價格": {"{:.1f}".format(float(kline_data[-1][1]))}, '
            f'"最高價格": {"{:.1f}".format(float(kline_data[-1][2]))}, '
            f'"最低價格": {"{:.1f}".format(float(kline_data[-1][3]))}, '
            f'"最新價格": {"{:.1f}".format(float(kline_data[-1][4]))}, '
            f'"成交量": {"{:.1f}".format(float(kline_data[-1][5]))}}}\n\n'
        )
        return formatted_data


    def get_prompt_for_new_order(self):
        """生成新的訂單提示詞"""
        #kline_info_5m = self.binance.get_kline_5m()
        kline_info_15m = self.binance.get_kline_15m()
        kline_info_1h = self.binance.get_kline_1h()
        price_info = self.binance.get_price()

        prompt = (
            "你是一名資深的高勝率交易員，需要通過分析歷史和當前K線數據來給出高準確度的買入和賣出建議。\n"
            "只有當你認為市場出現了極高勝率且收益潛力超過10%的機會時，才應該進行交易，否則應保持觀望。"
            "你的目標是用最少的交易次數實現最大的收益，避免頻繁交易帶來的手續費和市場波動風險。"
            "請以長期投資的視角對市場進行分析，尋找能夠穩健帶來收益的交易機會，而不是追求短期的小幅波動。"
            "所有交易策略應基於確定性極高的趨勢，並以最少的交易次數實現收益最大化。"
            "請注意：\n"
            "1. 由於交易手續費高昂，確保每次買入後的價格波動幅度能有效超過交易成本，並最大化收益。\n"
            "2. 對於賣出，確保在價格達到高點或回調前及時出場，避免錯失盈利機會。\n"
            "3. 在進行交易建議時，需確保交易計畫包含合理的止損與止盈策略，以最大化資金使用效率並降低風險。\n\n"
            "4. 本次交易將使用20倍槓桿，請謹慎操作。\n"
            f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 當前價格: {price_info['symbol']} = {"{:.1f}".format(float(price_info['price']))}\n\n"
        )

        #prompt += self.format_kline_data(kline_info_5m, "5分鐘")
        prompt += self.format_kline_data(kline_info_15m, "15分鐘")
        prompt += self.format_kline_data(kline_info_1h, "1小時")

        prompt += (
            "請根據以上市場資訊進行分析，並回答以下問題：\n"
            "1. 當前是否存在合適的交易機會？\n"
            "2. 如果存在交易機會，請提供建議的買入或賣出方向、價格範圍，以及對應的止損和止盈價格。\n"
            "3. 請簡要描述你的交易建議背後的原因和邏輯。\n\n"
            "請嚴格遵守以下格式回覆：\n"
            '{"交易機會": "是/否", "action": "BUY/SELL/null", "建議價格": 價格, '
            '"止損價格": 價格, "止盈價格": 價格, "交易理由": "原因描述"}\n'
        )
        return prompt
    

    def get_prompt_for_stop_loss_or_take_profit(self, order_record):
        target_pnl=0.20
        """生成止盈或止損的提示詞，目標收益率默認為 20%"""
        if order_record is None:
            return "無法加載訂單記錄。"

        print("查詢歷史K線資訊...")
        #kline_info_5m = self.binance.get_kline_5m()
        kline_info_15m = self.binance.get_kline_15m()
        kline_info_1h = self.binance.get_kline_1h()
        price_info = self.binance.get_price()

        # 初始化提示詞
        prompt = (
            "你是一名資深的高勝率交易員，需要通過分析歷史和當前K線數據來給出高準確度的買入和賣出建議。\n"
            "只有當你認為市場出現了極高勝率且收益潛力超過10%的機會時，才應該進行交易，否則應保持觀望。"
            "你的目標是用最少的交易次數實現最大的收益，避免頻繁交易帶來的手續費和市場波動風險。"
            "請以長期投資的視角對市場進行分析，尋找能夠穩健帶來收益的交易機會，而不是追求短期的小幅波動。"
            "所有交易策略應基於確定性極高的趨勢，並以最少的交易次數實現收益最大化。"
            "注意：\n"
            " 1. 目前的訂單已經在執行中，止盈或止損將會產生高額的手續費，所以請謹慎的設定止盈止損價格。\n"
            " 2. 確保在價格達到高點或回調前及時出場，避免錯失盈利機會\n"
            " 3. 除非有明顯反轉趨勢可能導致虧損，否則不輕易以市價止損。\n"
            " 4. 目前的訂單開啟了20倍槓桿。\n"
            f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 當前價格: {price_info['symbol']} = {"{:.1f}".format(float(price_info['price']))}\n\n"
        )

        #prompt += self.format_kline_data(kline_info_5m, "5分鐘")
        prompt += self.format_kline_data(kline_info_15m, "15分鐘")
        prompt += self.format_kline_data(kline_info_1h, "1小時")

        # 訂單資訊
        main_order = order_record.get('main_order')
        stop_loss_order = order_record.get('stop_loss_order')
        take_profit_order = order_record.get('take_profit_order')

        unrealized_profit = float(main_order['unRealizedProfit'])  # 未實現盈虧
        notional = float(main_order['notional'])  # 持倉名義金額
        leverage = 20  # 杠杆倍數

        # 計算收益率
        pnl_rate = unrealized_profit / (notional / leverage)
        if take_profit_order['stopPrice'] < stop_loss_order['stopPrice']:
            pnl_rate = -pnl_rate

        # 是否達到目標收益率
        is_target_reached = pnl_rate >= target_pnl
        if pnl_rate > 0.1 and pnl_rate < 0.2:
            prompt += (
                "訂單已經有5%以上浮盈，可以視市場情況考慮適當調整止損價格以保護利潤。\n"
            )
        if is_target_reached:
            prompt += (
                "\n提示：當前收益率已達到初始目標（20%）。請考慮是否止盈或是提高止損以保護收益，"
            )
        prompt += (
            "以下為當前訂單資訊，請根據市場趨勢強弱、當前盈利狀況和風險收益比，優化止盈止損設定，並避免因不明顯的趨勢而進行過於保守的調整。：\n"
            f"  - 開單時間：{datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 訂單方向：{'做多' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else '做空'}\n"
            f"  - 主訂單價格：{main_order['entryPrice']}\n"
            f"  - 止損價格：{stop_loss_order.get('stopPrice')}\n"
            f"  - 止盈價格：{take_profit_order.get('stopPrice')}\n"
            f"  - 當前收益率：{pnl_rate:.2%}\n"
            f"  - 目標收益率：{target_pnl * 100:.2f}%\n"
            f"  - 是否達標：{'是' if is_target_reached else '否'}\n"
        )

        print(
            "以下為當前訂單資訊，請根據市場趨勢強弱、當前盈利狀況和風險收益比，優化止盈止損設定，並避免因不明顯的趨勢而進行過於保守的調整。：\n"
            f"  - 開單時間：{datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 訂單方向：{'做多' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else '做空'}\n"
            f"  - 主訂單價格：{main_order['entryPrice']}\n"
            f"  - 止損價格：{stop_loss_order.get('stopPrice')}\n"
            f"  - 止盈價格：{take_profit_order.get('stopPrice')}\n"
            f"  - 當前收益率：{pnl_rate:.2%}\n"
            f"  - 目標收益率：{target_pnl * 100:.2f}%\n"
            f"  - 是否達標：{'是' if is_target_reached else '否'}\n"
        )
        prompt += (
            "請謹慎評估是否需要進行操作，並且確保該操作能夠帶來足夠的收益或損失減少以抵消手續費影響。\n\n"
            "\n若你認為應當以市價立即止盈止損，請嚴格按照以下格式回覆：\n"
            '{"action": "STOP_LOSS_TAKE_PROFIT", "理由": "說明你對當前市場狀態的分析"}\n'
            "\n若你認為應當修改止盈止損訂單，請嚴格按照以下格式回覆：\n"
            '{"action": "MODIFY_STOP_LOSS_TAKE_PROFIT", "止損價格": 價格(int), "止盈價格": 價格(int), "交易理由": "說明為何選擇交易"}\n'
            "\n若你認為無需做任何更動，請嚴格按照以下格式回覆：\n"
            '{"action": null, "理由": "說明你對當前市場狀態的分析"}\n'
        )

        return prompt
    
    def get_prompt_for_cancel_order(self, order_record):
        """生成取消訂單的提示詞"""
        # order_record = self.load_order_record()
        if order_record is None:
            return "無法加載訂單記錄。"

        print("查詢歷史K線資訊...")
        #kline_info_5m = self.binance.get_kline_5m()
        kline_info_15m = self.binance.get_kline_15m()
        kline_info_1h = self.binance.get_kline_1h()
        price_info = self.binance.get_price()

        prompt = (
            "你是一名資深的高勝率交易員，需要通過分析歷史和當前K線數據來給出高準確度的買入和賣出建議。\n"
            "請注意：\n"
            "1. 當訂單未成立時，不會產生手續費，但取消訂單可能會影響後續交易策略的靈活性。\n"
            "2. 若市場已顯示出明顯的反轉信號或風險超出預期，應考慮取消訂單，降低潛在虧損風險。\n"
            "3. 若當前價格已經遠離訂單價格，該訂單已無成交機會，應立即取消訂單。\n"
            "4. 如果判斷市場仍有潛在機會，則應保留訂單並靜待市場變化。\n"
            "請根據以下資訊進行分析並提供建議。\n\n"
            f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 當前價格: {price_info['symbol']} = {"{:.1f}".format(float(price_info['price']))}\n\n"
        )

        #prompt += self.format_kline_data(kline_info_5m, "5分鐘")
        prompt += self.format_kline_data(kline_info_15m, "15分鐘")
        prompt += self.format_kline_data(kline_info_1h, "1小時")

        main_order = order_record.get('main_order')
        stop_loss_order = order_record.get('stop_loss_order')
        take_profit_order = order_record.get('take_profit_order')

        prompt += (
            "請你針對以下未成立訂單給出專業建議。\n"
            "當前訂單資訊為：\n"
            f"  - 訂單開單時間：{datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
            f"  - 當前訂單為：{'做多' if take_profit_order['stopPrice']>stop_loss_order['stopPrice'] else '做空'}\n"
            f"  - 主訂單價格：{main_order['price']}\n"
            f"  - 止損價格：{stop_loss_order['stopPrice']}\n"
            f"  - 止盈價格：{take_profit_order['stopPrice']}\n\n"
        )

        prompt += (
            "請根據以上市場數據與訂單資訊回答以下問題：\n"
            "1. 是否需要取消這些尚未成立的訂單？\n"
            "2. 若建議取消訂單，請具體說明市場變化和相關風險的判斷依據。\n\n"
            "請嚴格遵守以下格式回覆：\n"
            '{"action": "CANCEL_ORDER/KEEP_ORDER", "理由": "解釋你的決策理由"}\n'
        )

        return prompt


    # def get_prompt_for_stop_loss_or_take_profit(self, order_record):
    #     """生成止盈或止損的提示詞"""
    #     if order_record is None:
    #         return "無法加載訂單記錄。"

    #     print("查詢歷史K線資訊...")
    #     kline_info_15m = self.binance.get_kline_15m()
    #     kline_info_1h = self.binance.get_kline_1h()
    #     price_info = self.binance.get_price()

    #     prompt = (
    #         "你是一名來自華爾街的高勝率交易員，現在你需要針對市場行情進行判斷，"
    #         "並且展示你的能力使你的投資人願意加大投資你的力度。\n"
    #         "希望你在盡可能縮小損失的同時盡可能地擴大收益，並且考慮到手續費問題，交易頻率越低越好。\n"
    #         "你的目標是使得目標收益率盡可能大於等於20%。\n\n"
    #         "注意：目前的訂單已經在執行中，提前止盈或止損將會產生額外的手續費。\n"
    #         "請謹慎評估是否需要進行操作，並且確保該操作能夠帶來足夠的收益或損失減少以抵消手續費影響。\n\n"
    #         "如果你認為市場趨勢即將出現明顯的反轉，並且當前持倉的繼續存在可能導致潛在虧損，"
    #         "即使當前收益未達到預期，也應果斷選擇即時止盈止損，以確保避免進一步損失。\n"
    #         f"  - 現在時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    #         f"  - 當前價格: {price_info['symbol']} = {"{:.1f}".format(float(price_info['price']))}\n\n"
    #     )
    #     prompt += self.format_kline_data(kline_info_15m, "15分鐘")
    #     prompt += self.format_kline_data(kline_info_1h, "1小時")

    #     main_order = order_record.get('main_order')
    #     stop_loss_order = order_record.get('stop_loss_order')
    #     take_profit_order = order_record.get('take_profit_order')

    #     print(main_order)

    #     unrealized_profit = float(main_order['unRealizedProfit'])  # 未實現盈虧
    #     notional = float(main_order['notional'])  # 持倉數量
    #     leverage = 20 # 杠杆倍數

    #     # 計算收益率
    #     pnl_rate = unrealized_profit / (notional / leverage)

    #     # 新增收益目標的條件描述
    #     target_pnl = 0.20  # 目標收益率 20%
    #     is_target_reached = pnl_rate >= target_pnl

    #     prompt += (
    #         "當前訂單資訊為：\n"
    #         f"  - 當前訂單開單時間為：{datetime.fromtimestamp(main_order['updateTime'] / 1000).strftime('%Y-%m-%d %H:%M:%S')}\n"
    #         f"  - 當前訂單為：{'做多' if take_profit_order['stopPrice'] > stop_loss_order['stopPrice'] else '做空'}\n"
    #         f"  - 主訂單價格：{main_order['entryPrice']}\n"
    #         f"  - 止損訂單價格：{stop_loss_order['stopPrice']}\n"
    #         f"  - 止盈訂單價格：{take_profit_order['stopPrice']}\n"
    #         f"  - 當前訂單收益率為：{pnl_rate:.2%}\n"
    #         f"  - 目標收益率為：{target_pnl * 100:.2f}%\n"
    #         f"  - 是否已達目標收益率：{'是' if is_target_reached else '否'}\n"
    #     )

    #     # 添加判斷條件的描述與操作建議
    #     prompt += (
    #         "若你認為應當立即以市價止盈止損，請嚴格遵守以下格式回覆：\n"
    #         '{"action": "STOP_LOSS_TAKE_PROFIT", "理由": "說明你對當前市場狀態的分析"}\n'
    #         "若是你認為應當修改止盈止損價格，盡可能保持利潤，請嚴格遵守以下格式回覆：\n"
    #         '{"action": "MODIFY_STOP_LOSS_TAKE_PROFIT", "止損價格": 價格(int), "止盈價格": 價格(int), "交易理由": "說明為何選擇交易"}\n'
    #         "若是你認為當前訂單價格無須更動，請嚴格遵守以下格式回覆：\n"
    #         '{"action": null, "理由": "說明你對當前市場狀態的分析"}\n'
    #     )

    #     # 根據目標收益率動態提示是否建議操作
    #     if is_target_reached:
    #         prompt += (
    #             "\n警告：當前收益率已達到目標（20%），請評估是否應進行止盈操作以鎖定收益。\n"
    #         )
    #     elif pnl_rate < 0:
    #         prompt += (
    #             "\n提醒：當前處於虧損狀態，請密切關注市場走勢，必要時進行止損以控制損失。\n"
    #         )

    #     return prompt



