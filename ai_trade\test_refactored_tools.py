#!/usr/bin/env python3
"""
測試重構後的 LangChain 工具
"""

import os
import sys
from datetime import datetime

# 添加當前目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_binance_tools():
    """測試 Binance API 工具"""
    print("=== 測試 Binance API 工具 ===")

    try:
        # 測試導入
        print("1. 測試模組導入...")
        from binance_api import (
            get_price, get_kline_5m, get_trading_status,
            get_open_futures_orders, BinanceAPI, set_default_symbol
        )
        print("   ✓ 所有函數導入成功")

        # 測試工具函數存在
        print("2. 測試工具函數...")
        print(f"   ✓ get_price: {get_price}")
        print(f"   ✓ get_kline_5m: {get_kline_5m}")
        print(f"   ✓ set_default_symbol: {set_default_symbol}")

        # 測試兼容性類
        print("3. 測試兼容性類...")
        binance = BinanceAPI('ETHUSDT')
        print(f"   ✓ BinanceAPI 類實例化成功: {binance}")

        print("✅ Binance API 工具測試通過（結構驗證）")
        return True

    except Exception as e:
        print(f"❌ Binance API 工具測試失敗: {e}")
        return False

def test_technical_indicators():
    """測試技術指標工具"""
    print("\n=== 測試技術指標工具 ===")

    try:
        # 測試導入
        print("1. 測試模組導入...")
        from calculate_indicators import (
            get_kline_and_calculate_indicators,
            TechnicalIndicators
        )
        print("   ✓ 技術指標模組導入成功")

        # 測試工具函數
        print("2. 測試工具函數...")
        print(f"   ✓ get_kline_and_calculate_indicators: {get_kline_and_calculate_indicators}")

        # 測試兼容性類
        print("3. 測試兼容性類...")
        tech_indicators = TechnicalIndicators('ETHUSDT', '5分鐘')
        print(f"   ✓ TechnicalIndicators 類實例化成功: {tech_indicators}")

        print("✅ 技術指標工具測試通過（結構驗證）")
        return True

    except Exception as e:
        print(f"❌ 技術指標工具測試失敗: {e}")
        return False

def test_langchain_agent():
    """測試 LangChain Agent"""
    print("\n=== 測試 LangChain Agent ===")

    try:
        # 測試導入
        print("1. 測試模組導入...")
        from langchain_agent import (
            LangchainAgent, get_market_data,
            get_technical_indicators_for_timeframe
        )
        print("   ✓ LangChain Agent 模組導入成功")

        # 測試工具函數
        print("2. 測試工具函數...")
        print(f"   ✓ get_market_data: {get_market_data}")
        print(f"   ✓ get_technical_indicators_for_timeframe: {get_technical_indicators_for_timeframe}")

        # 測試 Agent 類
        print("3. 測試 LangChain Agent...")
        agent = LangchainAgent(symbol='ETHUSDT')
        tools = agent.get_available_tools()
        print(f"   ✓ 可用工具數量: {len(tools)}")
        print(f"   ✓ 工具名稱: {[tool.name for tool in tools]}")

        print("✅ LangChain Agent 測試通過（結構驗證）")
        return True

    except Exception as e:
        print(f"❌ LangChain Agent 測試失敗: {e}")
        return False

def test_main_integration():
    """測試主程序集成"""
    print("\n=== 測試主程序集成 ===")
    
    try:
        from main import load_order_record, round_up
        
        # 測試輔助函數
        print("1. 測試輔助函數...")
        rounded_value = round_up(1.23456)
        print(f"   四捨五入結果: {rounded_value}")
        
        # 注意：load_order_record 需要真實的 API 連接，這裡只測試導入
        print("2. 測試訂單記錄函數導入...")
        print(f"   load_order_record 函數: {load_order_record}")
        
        print("✅ 主程序集成測試通過")
        return True
        
    except Exception as e:
        print(f"❌ 主程序集成測試失敗: {e}")
        return False

def main():
    """主測試函數"""
    print(f"開始測試重構後的專案 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    tests = [
        test_binance_tools,
        test_technical_indicators,
        test_langchain_agent,
        test_main_integration
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 60)
    print(f"測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有測試通過！重構成功！")
        return True
    else:
        print("⚠️  部分測試失敗，請檢查相關組件")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
