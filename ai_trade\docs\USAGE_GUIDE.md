# 增強版 LangChain 交易機器人使用指南

## 🚀 快速開始

### 1. 環境準備

#### 安裝依賴
```bash
pip install langchain langchain-google-genai langchain-core
pip install python-binance talib numpy pandas
pip install apscheduler
```

#### 設置環境變量
```bash
# Windows PowerShell
$env:GOOGLE_API_KEY="your_google_api_key"
$env:binance_api="your_binance_api_key"
$env:binance_key="your_binance_secret_key"

# Linux/Mac
export GOOGLE_API_KEY="your_google_api_key"
export binance_api="your_binance_api_key"
export binance_key="your_binance_secret_key"
```

### 2. 基本使用

#### 啟動增強版交易機器人
```python
from enhanced_main import EnhancedTradingBot

# 創建機器人
bot = EnhancedTradingBot(
    symbol='ETHUSDT',                    # 交易對
    model_name="gemini-1.5-flash-latest" # AI 模型
)

# 開始自動交易（每150秒分析一次）
bot.start_trading(interval_seconds=150)
```

#### 手動執行分析
```python
from advanced_langchain_agent import AdvancedTradingAgent

# 創建代理
agent = AdvancedTradingAgent(symbol='ETHUSDT')

# 分析新交易機會
result = agent.execute_trading_analysis("new_order")
print("新訂單分析結果:", result)

# 管理現有持倉
result = agent.execute_trading_analysis("position_management")
print("持倉管理結果:", result)
```

## 🧠 記憶和學習功能

### 查看交易記憶
```python
# 獲取記憶總結
memory_summary = agent.get_memory_summary()
print("交易記憶總結:", memory_summary)

# 獲取對話歷史
conversation_history = agent.get_conversation_history()
print(f"對話記錄數量: {len(conversation_history)}")

# 清除對話歷史（如需要）
agent.clear_conversation_history()
```

### 手動添加決策記錄
```python
# 訪問記憶管理器
memory_manager = agent.memory_manager

# 添加決策記錄
memory_manager.add_decision(
    decision_type="manual_analysis",
    reasoning="手動分析市場趨勢",
    market_context={"price": 3000, "trend": "bullish"},
    result="決定等待更好的入場點"
)

# 更新策略上下文
memory_manager.update_strategy_context("risk_level", "conservative")
```

## 🛠 工具使用

### 直接使用增強版工具
```python
from enhanced_trading_tools import (
    enhanced_get_market_data,
    enhanced_get_technical_indicators,
    enhanced_execute_buy_order,
    enhanced_execute_sell_order
)

# 獲取市場數據
market_data = enhanced_get_market_data('ETHUSDT')
print("市場數據:", market_data)

# 獲取技術指標
indicators = enhanced_get_technical_indicators('5m', 'ETHUSDT')
print("技術指標:", indicators)

# 執行交易（謹慎使用！）
# buy_result = enhanced_execute_buy_order(
#     price=3000.0,
#     stop_loss_price=2950.0,
#     take_profit_price=3100.0,
#     reasoning="技術指標顯示突破信號"
# )
```

### 參數驗證
```python
from enhanced_trading_tools import TradingToolsValidator

validator = TradingToolsValidator()

# 驗證價格
is_valid_price = validator.validate_price(3000.0, 'ETHUSDT')
print(f"價格驗證結果: {is_valid_price}")

# 驗證止損止盈設置
is_valid, message = validator.validate_stop_loss_take_profit(
    entry_price=3000.0,
    stop_loss=2950.0,
    take_profit=3100.0,
    side='BUY'
)
print(f"止損止盈驗證: {is_valid}, 信息: {message}")
```

## 📊 監控和狀態

### 獲取交易狀態
```python
from enhanced_main import EnhancedTradingBot

bot = EnhancedTradingBot(symbol='ETHUSDT')

# 獲取當前交易狀態
status = bot.get_current_trading_status()
print("交易狀態:", status)

# 獲取詳細狀態報告
report = bot.get_status_report()
print("狀態報告:", report)

# 清理孤立訂單
bot.cleanup_orphaned_orders()
```

### 錯誤處理和恢復
```python
# 機器人內建錯誤處理
bot = EnhancedTradingBot(symbol='ETHUSDT')

# 設置最大連續錯誤次數
bot.max_consecutive_errors = 5

# 啟動時會自動處理錯誤和恢復
bot.start_trading()
```

## 🎯 高級配置

### 自定義記憶類型
```python
# 使用不同的記憶模式
agent = AdvancedTradingAgent(
    symbol='ETHUSDT',
    memory_type="buffer"          # 或 "buffer_summary"
)
```

### 自定義提示模板
```python
from advanced_prompts import AdvancedPromptTemplates

# 獲取系統提示
system_prompt = AdvancedPromptTemplates.get_system_prompt_template()

# 獲取新訂單分析模板
new_order_template = AdvancedPromptTemplates.get_new_order_analysis_template()

# 創建動態提示
context = {"current_time": "2024-01-01 12:00:00", "symbol": "ETHUSDT"}
prompt = AdvancedPromptTemplates.create_dynamic_prompt("new_order", context)
```

## 🧪 測試和調試

### 運行測試
```bash
# 基礎功能測試
python simple_test_enhanced.py

# 完整系統測試
python test_enhanced_system.py
```

### 調試模式
```python
# 啟用詳細日誌
agent = AdvancedTradingAgent(symbol='ETHUSDT')
agent.agent_executor.verbose = True

# 查看工具列表
tools = agent.get_available_tools()
for tool in tools:
    print(f"工具: {tool.name} - {tool.description}")
```

## ⚠️ 重要注意事項

### 風險管理
1. **測試環境**: 建議先在測試網或小額資金測試
2. **參數驗證**: 系統會自動驗證交易參數，但請仔細檢查
3. **止損設置**: 確保每筆交易都設置合理的止損
4. **資金管理**: 單筆交易風險不超過總資金的2%

### API 限制
1. **頻率限制**: 避免過於頻繁的 API 調用
2. **網絡穩定**: 確保網絡連接穩定
3. **API 密鑰**: 妥善保管 API 密鑰，設置適當權限

### 系統維護
1. **記憶清理**: 定期清理過舊的記憶文件
2. **日誌監控**: 定期檢查錯誤日誌
3. **性能監控**: 監控系統資源使用情況

## 🔧 故障排除

### 常見問題

#### 1. 導入錯誤
```bash
# 確保安裝了所有依賴
pip install -r requirements.txt

# 檢查 Python 路徑
python -c "import sys; print(sys.path)"
```

#### 2. API 認證失敗
```bash
# 檢查環境變量
echo $GOOGLE_API_KEY
echo $binance_api

# 重新設置環境變量
export GOOGLE_API_KEY="your_key_here"
```

#### 3. 記憶文件損壞
```python
# 重置記憶文件
import os
if os.path.exists('trading_memory_ETHUSDT.json'):
    os.remove('trading_memory_ETHUSDT.json')
```

#### 4. 網絡連接問題
```python
# 測試網絡連接
from binance_api import get_price
try:
    price = get_price('ETHUSDT')
    print("網絡連接正常")
except Exception as e:
    print(f"網絡連接問題: {e}")
```

## 📞 支持

如果遇到問題：
1. 檢查日誌文件
2. 運行測試腳本
3. 查看錯誤信息
4. 檢查環境配置

系統設計為高度可觀測和可調試，大部分問題都可以通過日誌和狀態報告定位。
