"""
簡化版增強系統測試
不依賴外部庫，專注於核心功能測試
"""

import sys
import os
import json
from datetime import datetime

def test_imports():
    """測試基本導入"""
    print("🧪 測試基本導入...")
    
    try:
        # 測試 LangChain 導入
        from langchain.memory import ConversationBufferMemory
        from langchain_core.tools import tool
        from langchain_google_genai import ChatGoogleGenerativeAI
        print("✅ LangChain 組件導入成功")
        
        # 測試現有模組導入
        from core.binance_api import get_price, BinanceAPI
        print("✅ Binance API 模組導入成功")
        
        return True
        
    except ImportError as e:
        print(f"❌ 導入失敗: {str(e)}")
        return False
    except Exception as e:
        print(f"❌ 其他錯誤: {str(e)}")
        return False


def test_memory_system():
    """測試記憶系統基本功能"""
    print("\n📝 測試記憶系統...")
    
    try:
        # 創建測試記憶文件
        test_memory = {
            'trading_sessions': [],
            'decision_history': [
                {
                    'timestamp': datetime.now().isoformat(),
                    'decision_type': 'test',
                    'reasoning': '測試決策',
                    'result': '測試結果'
                }
            ],
            'strategy_context': {'test_key': 'test_value'}
        }
        
        # 寫入測試文件
        with open('test_memory.json', 'w', encoding='utf-8') as f:
            json.dump(test_memory, f, ensure_ascii=False, indent=2)
        
        # 讀取測試文件
        with open('test_memory.json', 'r', encoding='utf-8') as f:
            loaded_memory = json.load(f)
        
        if len(loaded_memory['decision_history']) > 0:
            print("✅ 記憶系統讀寫功能正常")
            
            # 清理測試文件
            os.remove('test_memory.json')
            return True
        else:
            print("❌ 記憶系統數據異常")
            return False
            
    except Exception as e:
        print(f"❌ 記憶系統測試失敗: {str(e)}")
        return False


def test_tool_structure():
    """測試工具結構"""
    print("\n🛠 測試工具結構...")
    
    try:
        from langchain_core.tools import tool
        
        # 創建測試工具
        @tool
        def test_tool(input_text: str) -> str:
            """測試工具
            
            Args:
                input_text: 輸入文本
                
            Returns:
                處理後的文本
            """
            return f"處理結果: {input_text}"
        
        # 測試工具調用
        result = test_tool("測試輸入")
        
        if "處理結果" in result:
            print("✅ 工具結構和調用正常")
            return True
        else:
            print("❌ 工具調用結果異常")
            return False
            
    except Exception as e:
        print(f"❌ 工具結構測試失敗: {str(e)}")
        return False


def test_prompt_templates():
    """測試提示模板"""
    print("\n📋 測試提示模板...")
    
    try:
        from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
        
        # 創建測試提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一個測試助手"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        # 測試模板格式化
        formatted = prompt.format_messages(
            input="測試輸入",
            agent_scratchpad=[]
        )
        
        if len(formatted) >= 2:
            print("✅ 提示模板創建和格式化正常")
            return True
        else:
            print("❌ 提示模板格式化異常")
            return False
            
    except Exception as e:
        print(f"❌ 提示模板測試失敗: {str(e)}")
        return False


def test_binance_connection():
    """測試幣安連接"""
    print("\n🔗 測試幣安連接...")
    
    try:
        from core.binance_api import get_price

        # 測試獲取價格
        price_info = get_price('ETHUSDT')
        
        if 'price' in price_info and 'symbol' in price_info:
            print(f"✅ 幣安連接正常，ETHUSDT 價格: {price_info['price']}")
            return True
        else:
            print("❌ 幣安 API 返回數據格式異常")
            return False
            
    except Exception as e:
        print(f"❌ 幣安連接測試失敗: {str(e)}")
        return False


def test_agent_creation():
    """測試代理創建"""
    print("\n🤖 測試代理創建...")
    
    try:
        from langchain.agents import create_tool_calling_agent
        from langchain_google_genai import ChatGoogleGenerativeAI
        from langchain_core.prompts import ChatPromptTemplate, MessagesPlaceholder
        from langchain_core.tools import tool
        
        # 創建簡單的 LLM
        llm = ChatGoogleGenerativeAI(
            model="gemini-1.5-flash-latest",
            temperature=0
        )
        
        # 創建測試工具
        @tool
        def simple_test_tool(query: str) -> str:
            """簡單測試工具"""
            return f"測試回應: {query}"
        
        tools = [simple_test_tool]
        
        # 創建提示模板
        prompt = ChatPromptTemplate.from_messages([
            ("system", "你是一個測試代理"),
            ("human", "{input}"),
            MessagesPlaceholder(variable_name="agent_scratchpad")
        ])
        
        # 創建代理
        agent = create_tool_calling_agent(llm, tools, prompt)
        
        if agent is not None:
            print("✅ 代理創建成功")
            return True
        else:
            print("❌ 代理創建失敗")
            return False
            
    except Exception as e:
        print(f"❌ 代理創建測試失敗: {str(e)}")
        return False


def run_all_tests():
    """運行所有測試"""
    print("🚀 開始增強版系統簡化測試")
    print("=" * 50)
    
    tests = [
        ("基本導入", test_imports),
        ("記憶系統", test_memory_system),
        ("工具結構", test_tool_structure),
        ("提示模板", test_prompt_templates),
        ("幣安連接", test_binance_connection),
        ("代理創建", test_agent_creation)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} 測試執行異常: {str(e)}")
    
    print("\n" + "=" * 50)
    print(f"📊 測試結果: {passed}/{total} 通過")
    
    if passed == total:
        print("🎉 所有基礎測試通過！系統基礎架構正常！")
        return True
    elif passed >= total * 0.8:
        print("⚠️ 大部分測試通過，系統基本可用")
        return True
    else:
        print("❌ 多項測試失敗，請檢查系統配置")
        return False


def main():
    """主函數"""
    try:
        success = run_all_tests()
        
        if success:
            print("\n✅ 基礎測試完成，系統準備就緒")
            print("💡 建議：")
            print("   1. 檢查 API 密鑰配置")
            print("   2. 確認網絡連接正常")
            print("   3. 運行完整功能測試")
        else:
            print("\n❌ 基礎測試未完全通過")
            print("💡 建議：")
            print("   1. 檢查依賴庫安裝")
            print("   2. 確認環境變量設置")
            print("   3. 查看錯誤日誌")
        
        return success
        
    except Exception as e:
        print(f"❌ 測試執行失敗: {str(e)}")
        return False


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
